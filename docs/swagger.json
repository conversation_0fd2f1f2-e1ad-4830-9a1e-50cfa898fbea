{"swagger": "2.0", "info": {"contact": {}}, "paths": {"/api/v1/sse": {"post": {"description": "处理流式研究请求并返回SSE流", "consumes": ["application/json"], "produces": ["text/event-stream"], "tags": ["research"], "summary": "处理流式研究请求", "parameters": [{"description": "Research request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.Request"}}], "responses": {"200": {"description": "SSE流", "schema": {"type": "string"}}}}}, "/health": {"get": {"description": "Kubernetes 健康检查探针", "produces": ["text/plain"], "tags": ["health"], "summary": "健康检查", "responses": {"200": {"description": "ok", "schema": {"type": "string"}}}}}}, "definitions": {"service.Request": {"type": "object", "properties": {"content": {"type": "string"}}}}}