definitions:
  service.Request:
    properties:
      content:
        type: string
    type: object
info:
  contact: {}
paths:
  /api/v1/sse:
    post:
      consumes:
      - application/json
      description: 处理流式研究请求并返回SSE流
      parameters:
      - description: Research request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service.Request'
      produces:
      - text/event-stream
      responses:
        "200":
          description: SSE流
          schema:
            type: string
      summary: 处理流式研究请求
      tags:
      - research
  /health:
    get:
      description: Kubernetes 健康检查探针
      produces:
      - text/plain
      responses:
        "200":
          description: ok
          schema:
            type: string
      summary: 健康检查
      tags:
      - health
swagger: "2.0"
