apiVersion: apps/v1
kind: Deployment
metadata:
  name: talos-ignite
  labels:
    app: talos-ignite
spec:
  replicas: 1
  selector:
    matchLabels:
      app: talos-ignite
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: talos-ignite
    spec:
      containers:
        - name: talos-ignite
          image: docker-reg.devops.xiaohongshu.com/shequ/talos-frontend:v0.0.7-de8aef5a
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
              name: http
          env:
            - name: NODE_ENV
              value: "production"
            - name: NEXT_PUBLIC_API_URL
              value: "http://talos-agent:8080/api"
          resources:
            requests:
              cpu: "100m"
              memory: "128Mi"
            limits:
              cpu: "300m"
              memory: "256Mi"
          livenessProbe:
            tcpSocket:
              port: http
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 3
            failureThreshold: 3
          readinessProbe:
            tcpSocket:
              port: http
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 3
            failureThreshold: 3
---
apiVersion: v1
kind: Service
metadata:
  name: talos-ignite
  labels:
    app: talos-ignite
spec:
  type: ClusterIP
  ports:
    - port: 80
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app: talos-ignite
