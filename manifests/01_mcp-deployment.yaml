apiVersion: apps/v1
kind: Deployment
metadata:
  name: talos-mcp
  labels:
    app: talos-mcp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: talos-mcp
  template:
    metadata:
      labels:
        app: talos-mcp
    spec:
      containers:
        - name: talos-mcp
          image: docker-reg.devops.xiaohongshu.com/shequ/talos:v0.0.7-de8aef5a
          imagePullPolicy: Always
          args:
            ["mcp", "--type", "sse", "--port", "8080", "--read-only", "true"]
          ports:
            - containerPort: 8080
              name: http
          resources:
            requests:
              cpu: "100m"
              memory: "128Mi"
            limits:
              cpu: "500m"
              memory: "512Mi"
          livenessProbe:
            tcpSocket:
              port: http
            initialDelaySeconds: 5
            periodSeconds: 10
          readinessProbe:
            tcpSocket:
              port: http
            initialDelaySeconds: 5
            periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: talos-mcp
spec:
  selector:
    app: talos-mcp
  ports:
    - port: 8080
      targetPort: http
      name: http
