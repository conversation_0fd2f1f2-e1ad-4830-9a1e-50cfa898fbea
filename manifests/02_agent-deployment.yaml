apiVersion: apps/v1
kind: Deployment
metadata:
  name: talos-agent
  labels:
    app: talos-agent
spec:
  replicas: 1
  selector:
    matchLabels:
      app: talos-agent
  template:
    metadata:
      labels:
        app: talos-agent
    spec:
      containers:
        - name: talos-agent
          image: docker-reg.devops.xiaohongshu.com/shequ/talos:v0.0.7-de8aef5a
          imagePullPolicy: Always
          args: ["agent", "--port", "8080"]
          ports:
            - containerPort: 8080
              name: http
          env:
            - name: TALOS_GINMODE
              value: "release"
            - name: TALOS_SQUAD_LLMS_ALLIN_APIKEY
              value: "QSTe6b90fdabe517a1665f43447a490c8a8"
            - name: TALOS_SQUAD_LLMS_ALLIN_BASEURL
              value: "http://redservingapi.devops.xiaohongshu.com/v1"
            - name: TALOS_SQUAD_LLMS_ALLIN_MODEL
              value: "qwen3-32b"
            - name: TALOS_SQUAD_LLMS_ALLIN_TEMPERATURE
              value: "0.7"
            - name: TALOS_SQUAD_MCP_BASEURL
              value: "http://talos-mcp:8080/sse"
          resources:
            requests:
              cpu: "100m"
              memory: "128Mi"
            limits:
              cpu: "500m"
              memory: "512Mi"
          livenessProbe:
            httpGet:
              path: /healthz
              port: http
            initialDelaySeconds: 5
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /readyz
              port: http
            initialDelaySeconds: 5
            periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: talos-agent
spec:
  selector:
    app: talos-agent
  ports:
    - port: 8080
      targetPort: http
      name: http
