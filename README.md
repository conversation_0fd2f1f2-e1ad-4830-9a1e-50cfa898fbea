# Talos

Talos is a Go-based LLM agent framework providing enterprise-grade research and analysis capabilities through a RESTful API with streaming support.

## Features

- **RESTful API** with JSON and Server-Sent Events (SSE) support
- **Health Checks** for Kubernetes integration
- **Structured Logging** with request tracing
- **CORS Support** for web client integration
- **Swagger Documentation** for API exploration
- **Docker Support** for containerized deployment

## API Endpoints

### Health Checks

- `GET /healthz` - Liveness probe endpoint
- `GET /readyz` - Readiness probe endpoint

### Research API

- `POST /api/v1/research` - Submit a research request (synchronous)
- `POST /api/v1/stream` - Stream research results (SSE)

## Getting Started

### Prerequisites

- Go 1.24+
- Docker (for containerized deployment)
- Make (for development tasks)

### Installation

1. Clone the repository:

   ```bash
   git clone <repository-url>
   cd talos
   ```

2. Install dependencies:

   ```bash
   make deps
   ```

### Configuration

Create a `.env` file in the project root with the following variables:

```env
# Server Configuration
GIN_MODE=debug
PORT=8081

# Logging
LOG_LEVEL=info

# CORS
CORS_ALLOW_ORIGINS=*
```

### Running the Application

#### Development Mode

```bash
# Build and run the application
make run
```

#### Production Mode

```bash
# Build the application
make build

# Run the binary
./bin/talos
```

#### Using Docker

```bash
# Build the Docker image
docker build -t talos:latest .

# Run the container
docker run -p 8081:8081 talos:latest
```

### API Documentation

Once the application is running, you can access the Swagger documentation at:

```
http://localhost:8081/swagger/index.html
```

## Development

### Building the Project

```bash
# Build the application
make build

# Build for specific platform
make build-linux
make build-darwin
make build-windows
```

### Testing

```bash
# Run all tests
make test

# Run tests with race detector
make test-race

# Generate test coverage report
make coverage
```

### Linting and Formatting

```bash
# Run linter
make lint

# Format code
make fmt

# Check for unused dependencies
make tidy
```

## Project Structure

```
.
├── cmd/                # Command line entry points
├── config/            # Configuration loading and defaults
├── docs/              # API documentation
├── internal/          # Private application code
│   ├── api/           # HTTP handlers and routing
│   ├── service/       # Business logic
│   └── middleware/    # HTTP middleware
├── pkg/               # Reusable packages
├── testdata/          # Test fixtures
├── .env.example      # Example environment variables
├── Dockerfile         # Container configuration
├── Makefile           # Build and development tasks
└── go.mod            # Go module definition
```

## Dependencies

```shell
github.com/cohesion-org/deepseek-go v1.2.8
github.com/getkin/kin-openapi v0.118.0
github.com/ollama/ollama v0.5.12
github.com/mark3labs/mcp-go v0.27.0
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request
