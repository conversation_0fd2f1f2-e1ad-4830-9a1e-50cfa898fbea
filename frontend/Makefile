# Makefile for Talos Frontend

# Environment variables
DOCKER_REGISTRY ?= docker-reg.devops.xiaohongshu.com/shequ
IMAGE_NAME ?= talos-frontend
IMAGE_TAG ?= latest
FULL_IMAGE_NAME = $(DOCKER_REGISTRY)/$(IMAGE_NAME):$(IMAGE_TAG)
PLATFORM ?= linux/amd64

# Default target
.DEFAULT_GOAL := help

# Help
.PHONY: help
help: ## Show this help message
	@echo "Available targets:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}'
	@echo ""

# Colima
.PHONY: colima-start
colima-start: ## Start Colima with Docker
	colima start --cpu 4 --memory 8 --disk 50
	@echo "Colima started"

.PHONY: colima-start-k8s
colima-start-k8s: ## Start Colima with Kubernetes
	colima start --cpu 4 --memory 8 --disk 50 --kubernetes
	@echo "Colima with Kubernetes started"

.PHONY: colima-stop
colima-stop: ## Stop Colima
	colima stop
	@echo "Colima stopped"

.PHONY: buildx-setup
buildx-setup: ## Setup Docker Buildx builder
	docker buildx create --name mybuilder --use || true
	docker buildx inspect --bootstrap
	@echo "Buildx builder setup complete"

.PHONY: build
build: buildx-setup ## Build Docker image locally
	docker buildx build --platform $(PLATFORM) -t $(IMAGE_NAME):$(IMAGE_TAG) --load .
	@echo "Image built: $(IMAGE_NAME):$(IMAGE_TAG)"

.PHONY: build-multi
build-multi: buildx-setup ## Build multi-platform Docker image
	docker buildx build --platform linux/amd64,linux/arm64 -t $(IMAGE_NAME):$(IMAGE_TAG) --load .
	@echo "Multi-platform image built: $(IMAGE_NAME):$(IMAGE_TAG)"

.PHONY: tag
tag: ## Tag Docker image for registry
	docker tag $(IMAGE_NAME):$(IMAGE_TAG) $(FULL_IMAGE_NAME)
	@echo "Image tagged: $(FULL_IMAGE_NAME)"

.PHONY: push
push: ## Push Docker image to registry
	docker push $(FULL_IMAGE_NAME)
	@echo "Image pushed: $(FULL_IMAGE_NAME)"

.PHONY: build-push
build-push: buildx-setup ## Build and push Docker image directly
	docker buildx build --platform $(PLATFORM) -t $(FULL_IMAGE_NAME) --push .
	@echo "Image built and pushed: $(FULL_IMAGE_NAME)"

.PHONY: build-push-multi
build-push-multi: buildx-setup ## Build and push multi-platform Docker image
	docker buildx build --platform linux/amd64,linux/arm64 -t $(FULL_IMAGE_NAME) --push .
	@echo "Multi-platform image built and pushed: $(FULL_IMAGE_NAME)"

.PHONY: run
run: ## Run Docker container locally
	docker run -p 3000:3000 $(IMAGE_NAME):$(IMAGE_TAG)

.PHONY: clean
clean: ## Remove Docker image
	docker rmi $(IMAGE_NAME):$(IMAGE_TAG) || true
	docker rmi $(FULL_IMAGE_NAME) || true
	@echo "Images removed"

.PHONY: login
login: ## Login to Docker registry
	docker login $(DOCKER_REGISTRY) -u $(DOCKER_USER)
	@echo "Logged in to $(DOCKER_REGISTRY)"

.PHONY: buildx-prune
buildx-prune: ## Prune Buildx cache
	docker buildx prune -f
	@echo "Buildx cache pruned"
