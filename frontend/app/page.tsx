import { SearchInput } from "@/components/search-input"
import { Logo } from "@/components/logo"

export default function Home() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-950">
      <main className="flex w-full flex-1 flex-col items-center justify-center px-4 sm:px-20">
        <div className="w-full max-w-[800px] space-y-8">
          <div className="text-center relative">
            <Logo />
            <div className="relative inline-block">
              <h1 className="text-5xl font-bold tracking-tighter text-gray-900 dark:text-white sm:text-6xl">Ignite</h1>
              <div className="absolute -top-2 -right-2 transform translate-x-full">
                <span className="inline-block px-2 py-1 text-xs font-medium rounded-md bg-gray-200/80 text-gray-700 dark:bg-gray-800/80 dark:text-gray-300 shadow-sm">
                  Preview
                </span>
              </div>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">/ɪɡˈnaɪt/</p>
            <p className="text-lg text-gray-600 dark:text-gray-400 mt-4">你身边的 Kubernetes 智能小助手。</p>
          </div>
          <SearchInput />
        </div>
      </main>
      <footer className="w-full py-8 text-center text-sm text-gray-500 dark:text-gray-400">
        © {new Date().getFullYear()} RKE AI. 保留所有权利。
      </footer>
    </div>
  )
}
