"use client"

import { useState, useEffect, useRef } from "react"
import { ChevronDown, ChevronRight, Lightbulb } from "lucide-react"

interface ThinkingProcessProps {
  content: string
  isStreaming?: boolean
}

export function ThinkingProcess({ content, isStreaming = false }: ThinkingProcessProps) {
  const [isExpanded, setIsExpanded] = useState(true) // 默认展开以显示流式内容
  const contentRef = useRef<HTMLPreElement>(null)

  // 自动滚动到底部
  useEffect(() => {
    if (isStreaming && contentRef.current) {
      contentRef.current.scrollTop = contentRef.current.scrollHeight
    }
  }, [content, isStreaming])

  return (
    <div className="mb-4 border border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 text-left"
      >
        <div className="flex items-center">
          <Lightbulb className="h-5 w-5 text-orange-500 mr-2" />
          <span className="font-medium text-gray-700 dark:text-gray-300">
            思考过程
            {isStreaming && (
              <span className="ml-2 inline-flex items-center">
                <span className="animate-pulse text-orange-500">●</span>
                <span className="ml-1 text-xs text-gray-500">实时更新中</span>
              </span>
            )}
          </span>
        </div>
        {isExpanded ? (
          <ChevronDown className="h-5 w-5 text-gray-500" />
        ) : (
          <ChevronRight className="h-5 w-5 text-gray-500" />
        )}
      </button>
      {isExpanded && (
        <div className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
          <pre
            ref={contentRef}
            className="p-4 whitespace-pre-wrap text-sm text-gray-600 dark:text-gray-400 font-mono max-h-60 overflow-y-auto"
          >
            {content}
            {isStreaming && <span className="animate-pulse text-orange-500 ml-1">|</span>}
          </pre>
        </div>
      )}
    </div>
  )
}
