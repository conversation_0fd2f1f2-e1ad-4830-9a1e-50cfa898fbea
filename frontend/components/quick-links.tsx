import { ExternalLink } from "lucide-react"

interface QuickLink {
  name: string
  url: string
  description?: string
}

export function QuickLinks() {
  const links: QuickLink[] = [
    {
      name: "<PERSON><PERSON>",
      url: "https://rke.devops.xiaohongshu.com",
      description: "Red Kubernetes Engine",
    },
    {
      name: "Kubernetes",
      url: "https://kubernetes.io/",
      description: "Kubernetes 官方网站",
    },
    {
      name: "Kubernetes Github",
      url: "https://github.com/kubernetes/kubernetes",
      description: "Kubernetes 开源仓库",
    },
    {
      name: "CNCF Landscape",
      url: "https://landscape.cncf.io/",
      description: "云原生技术全景图",
    },
  ]

  return (
    <div className="w-full">
      <div className="flex flex-wrap gap-3 justify-center">
        {links.map((link) => (
          <a
            key={link.url}
            href={link.url}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-1.5 px-3 py-1.5 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors shadow-sm"
          >
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{link.name}</span>
            <ExternalLink className="h-3.5 w-3.5 text-gray-400" />
            {link.description && (
              <span className="text-xs text-gray-500 dark:text-gray-400 hidden sm:inline">- {link.description}</span>
            )}
          </a>
        ))}
      </div>
    </div>
  )
}
