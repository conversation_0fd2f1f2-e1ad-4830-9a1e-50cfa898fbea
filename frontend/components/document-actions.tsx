"use client"

import { useState } from "react"
import { <PERSON><PERSON>, Share, FileDown, ThumbsUp, ThumbsDown, Check } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface DocumentActionsProps {
  content: string
}

export function DocumentActions({ content }: DocumentActionsProps) {
  const [copied, setCopied] = useState(false)
  const [liked, setLiked] = useState(false)
  const [disliked, setDisliked] = useState(false)

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error("Failed to copy text: ", err)
    }
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: "Ignite 搜索结果",
          text: content.substring(0, 100) + "...",
          url: window.location.href,
        })
      } catch (err) {
        console.error("Error sharing: ", err)
      }
    } else {
      // 如果不支持 Web Share API，可以显示一个简单的分享对话框
      alert("分享链接已复制到剪贴板")
      navigator.clipboard.writeText(window.location.href)
    }
  }

  const handleExport = () => {
    // 这里是 PDF 导出的占位实现
    // 实际实现需要使用 jsPDF 或其他 PDF 生成库
    alert("PDF 导出功能即将推出")
  }

  const handleLike = () => {
    setLiked(!liked)
    if (disliked) setDisliked(false)
  }

  const handleDislike = () => {
    setDisliked(!disliked)
    if (liked) setLiked(false)
  }

  return (
    <div className="flex flex-col space-y-4">
      <div className="flex items-center space-x-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full" onClick={handleCopy}>
                {copied ? <Check className="h-4 w-4 text-green-500" /> : <Copy className="h-4 w-4 text-gray-500" />}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>{copied ? "已复制" : "复制内容"}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full" onClick={handleShare}>
                <Share className="h-4 w-4 text-gray-500" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>分享</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full" onClick={handleExport}>
                <FileDown className="h-4 w-4 text-gray-500" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>导出为 PDF</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className={`h-8 w-8 rounded-full ${liked ? "bg-gray-100 dark:bg-gray-800" : ""}`}
                onClick={handleLike}
              >
                <ThumbsUp className={`h-4 w-4 ${liked ? "text-orange-500" : "text-gray-500"}`} />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>有帮助</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className={`h-8 w-8 rounded-full ${disliked ? "bg-gray-100 dark:bg-gray-800" : ""}`}
                onClick={handleDislike}
              >
                <ThumbsDown className={`h-4 w-4 ${disliked ? "text-orange-500" : "text-gray-500"}`} />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>没帮助</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  )
}
