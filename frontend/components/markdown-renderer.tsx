"use client"

import ReactMarkdown from "react-markdown"
import { <PERSON>rism as <PERSON>ynta<PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-syntax-highlighter"
import { vscDarkPlus } from "react-syntax-highlighter/dist/cjs/styles/prism"
import { useEffect, useRef } from "react"
import { DocumentActions } from "./document-actions"

interface MarkdownRendererProps {
  content: string
  isStreaming?: boolean
}

export function MarkdownRenderer({ content, isStreaming = false }: MarkdownRendererProps) {
  const containerRef = useRef<HTMLDivElement>(null)

  // 自动滚动到底部
  useEffect(() => {
    if (isStreaming && containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight
    }
  }, [content, isStreaming])

  return (
    <div className="markdown-body bg-white dark:bg-gray-900 rounded-xl shadow-md overflow-hidden relative">
      {/* 头部状态指示器 */}
      {isStreaming && (
        <div className="flex items-center justify-between px-6 py-3 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <span className="animate-pulse text-orange-500 mr-2">●</span>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">正在生成回答...</span>
          </div>
        </div>
      )}

      {/* 内容区域 */}
      <div ref={containerRef} className="p-6 overflow-auto max-h-[70vh]">
        <ReactMarkdown
          components={{
            h1: ({ node, ...props }) => (
              <h1 className="text-2xl font-bold mt-6 mb-4 text-gray-900 dark:text-white" {...props} />
            ),
            h2: ({ node, ...props }) => (
              <h2 className="text-xl font-bold mt-5 mb-3 text-gray-900 dark:text-white" {...props} />
            ),
            h3: ({ node, ...props }) => (
              <h3 className="text-lg font-bold mt-4 mb-2 text-gray-900 dark:text-white" {...props} />
            ),
            p: ({ node, ...props }) => <p className="my-3 text-gray-700 dark:text-gray-300" {...props} />,
            ul: ({ node, ...props }) => (
              <ul className="list-disc pl-6 my-3 text-gray-700 dark:text-gray-300" {...props} />
            ),
            ol: ({ node, ...props }) => (
              <ol className="list-decimal pl-6 my-3 text-gray-700 dark:text-gray-300" {...props} />
            ),
            li: ({ node, ...props }) => <li className="my-1" {...props} />,
            a: ({ node, ...props }) => (
              <a className="text-orange-500 hover:text-orange-600 hover:underline" {...props} />
            ),
            blockquote: ({ node, ...props }) => (
              <blockquote
                className="border-l-4 border-gray-200 dark:border-gray-700 pl-4 my-3 text-gray-600 dark:text-gray-400 italic"
                {...props}
              />
            ),
            code({ node, inline, className, children, ...props }) {
              const match = /language-(\w+)/.exec(className || "")
              return !inline && match ? (
                <SyntaxHighlighter
                  style={vscDarkPlus}
                  language={match[1]}
                  PreTag="div"
                  className="rounded-md my-3"
                  {...props}
                >
                  {String(children).replace(/\n$/, "")}
                </SyntaxHighlighter>
              ) : (
                <code className="bg-gray-100 dark:bg-gray-800 rounded px-1 py-0.5 text-sm" {...props}>
                  {children}
                </code>
              )
            },
            table: ({ node, ...props }) => (
              <div className="overflow-x-auto my-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700" {...props} />
              </div>
            ),
            thead: ({ node, ...props }) => <thead className="bg-gray-50 dark:bg-gray-800" {...props} />,
            tbody: ({ node, ...props }) => (
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700" {...props} />
            ),
            tr: ({ node, ...props }) => <tr className="hover:bg-gray-50 dark:hover:bg-gray-800" {...props} />,
            th: ({ node, ...props }) => (
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                {...props}
              />
            ),
            td: ({ node, ...props }) => (
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" {...props} />
            ),
            hr: ({ node, ...props }) => <hr className="my-6 border-gray-200 dark:border-gray-800" {...props} />,
            img: ({ node, ...props }) => <img className="max-w-full h-auto rounded-md my-4" {...props} />,
          }}
        >
          {content}
        </ReactMarkdown>

        {/* 流式输入光标 */}
        {isStreaming && content && <span className="animate-pulse text-orange-500 ml-1 text-lg">|</span>}
      </div>

      {/* 底部操作按钮 - 只在非流式状态下显示 */}
      {!isStreaming && content && (
        <div className="px-6 pb-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center">
            <DocumentActions content={content} />
          </div>
          <p className="text-xs text-gray-400 dark:text-gray-500 mt-4">Ignite 也可能会犯错。请核查重要信息。</p>
        </div>
      )}
    </div>
  )
}
