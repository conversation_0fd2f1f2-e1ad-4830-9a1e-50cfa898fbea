{"apiVersion": "v1", "kind": "Event", "metadata": {"creationTimestamp": "2025-03-25T09:25:05Z", "name": "agentsmith-service-default-xw47z.1830018030235155", "namespace": "data", "resourceVersion": "318195431", "uid": "979dc009-e51b-42b6-b75d-5e384b613825"}, "lastTimestamp": "2025-04-09T06:10:52Z", "message": "Unable to retrieve some image pull secrets (xhs-docker-registry); attempting to pull the image may not succeed.", "reason": "FailedToRetrieveImagePullSecret", "reportingComponent": "kubelet", "reportingInstance": "k8s-alsh-infra010144092207", "type": "Warning", "count": 17024, "eventTime": null, "firstTimestamp": "2025-03-25T09:25:05Z", "involvedObject": {"apiVersion": "v1", "kind": "Pod", "name": "agentsmith-service-default-xw47z", "namespace": "data", "resourceVersion": "35966701", "uid": "443d86e0-6f51-4d71-895c-1319aad07d7f"}, "source": {"component": "kubelet", "host": "k8s-alsh-infra010144092207"}}