KIND:       Event
VERSION:    v1

DESCRIPTION:
FIELDS:
     action <string>
     What action was taken/failed regarding to the Regarding object.

     apiVersion <string>
     APIVersion defines the versioned schema of this representation of an object.
     Servers should convert recognized schemas to the latest internal value, and
     may reject unrecognized values. More info:
     https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources

     count <integer>
     The number of times this event has occurred.

     eventTime <string>
     Time when this Event was first observed.

     firstTimestamp <string>
     The time at which the event was first recorded. (Time of server receipt is
     in TypeMeta.)

     involvedObject <ObjectReference> -required-
     The object that this event is about.

     kind <string>
     Kind is a string value representing the REST resource this object
     represents. Servers may infer this from the endpoint the client submits
     requests to. Cannot be updated. In CamelCase. More info:
     https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds

     lastTimestamp <string>
     The time at which the most recent occurrence of this event was recorded.

     message <string>
     A human-readable description of the status of this operation.

     metadata <ObjectMeta> -required-
     Standard object's metadata. More info:
     https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata

     reason <string>
     This should be a short, machine understandable string that gives the reason
     for the transition into the object's current status.

     related <ObjectReference>
     Optional secondary object for more complex actions.

     reportingComponent <string>
     Name of the controller that emitted this Event, e.g.
     `kubernetes.io/kubelet`.

     reportingInstance <string>
     ID of the controller instance, e.g. `kubelet-xyzf`.

     series <EventSeries>
     Data about the Event series this event represents or nil if it's a singleton
     Event.

     source <EventSource>
     The component reporting this event. Should be a short machine understandable
     string.

     type <string>
     Type of this event (Normal, Warning), new types could be added in the future
