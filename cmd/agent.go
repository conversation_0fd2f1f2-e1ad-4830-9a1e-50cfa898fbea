package cmd

import (
	"context"
	"fmt"
	"log/slog"
	"net/http"
	"os"

	"code.devops.xiaohongshu.com/cloud-native/talos/internal/api"
	talosctx "code.devops.xiaohongshu.com/cloud-native/talos/pkg/context"
	"github.com/spf13/cobra"
)

// serverPort is the port number for the server
var serverPort int

// agentCmd represents the agent command
var agentCmd = &cobra.Command{
	Use:   "agent",
	Short: "Start the Talos agent server",
	Long: `The agent command initiates the Talos agent server, which serves as the central hub for the Talos system.
It provides a RESTful API for managing and coordinating various Talos components, including the squad of specialist agents.
The server handles incoming requests, routes them to appropriate handlers, and manages the overall system state.

Example usage:
  talos agent [flags]

Available flags:
  -p, --port int   Set the server port (default 8081)`,
	Run: func(cmd *cobra.Command, args []string) {
		router, err := api.SetupRouter()
		if err != nil {
			slog.LogAttrs(context.Background(), slog.LevelError,
				"Failed to setup router", slog.Any("error", err))
			os.Exit(1)
		}

		server := &http.Server{
			Addr:    fmt.Sprintf(":%d", serverPort),
			Handler: router,
		}

		talosctx.RunServer(
			context.Background(),
			// Start the server
			func() error {
				slog.LogAttrs(context.Background(), slog.LevelInfo,
					"Starting server", slog.Int("port", serverPort))
				return server.ListenAndServe()
			},
			//  Shutdown the server
			func(ctx context.Context) error {
				slog.LogAttrs(context.Background(), slog.LevelInfo,
					"Shutting down server", slog.Int("port", serverPort))
				return server.Shutdown(ctx)
			},
		)
	},
}

func init() {
	rootCmd.AddCommand(agentCmd)

	// Here you will define your flags and configuration settings.

	// Cobra supports Persistent Flags which will work for this command
	// and all subcommands, e.g.:
	// agentCmd.PersistentFlags().String("foo", "", "A help for foo")

	// Cobra supports local flags which will only run when this command
	// is called directly, e.g.:
	// agentCmd.Flags().BoolP("toggle", "t", false, "Help message for toggle")
	agentCmd.Flags().IntVarP(&serverPort, "port", "p", 8081, "Set the server port")
}
