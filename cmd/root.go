package cmd

import (
	"context"
	"log/slog"
	"os"

	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/log"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/version"
	"github.com/spf13/cobra"
)

// LogLevel is the log level for the server
var logLevel int

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "talos",
	Short: "Talos: An LLM-powered network analysis and troubleshooting framework",
	Long: `Talos is a Go-based LLM agent framework providing enterprise-grade network analysis 
and troubleshooting capabilities through configurable host agents and specialist teams.

Key features include:
- Team-based Agent System with host agent coordination
- DeepSeek LLM integration for natural language understanding
- Specialized agents for event collection, network analysis, knowledge retrieval, and solution execution
- Kubernetes-native design for seamless integration with existing infrastructure`,
	// Uncomment the following line if your bare application
	// has an action associated with it:
	// Run: func(cmd *cobra.Command, args []string) { },
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
func Execute() {
	err := rootCmd.Execute()
	if err != nil {
		os.Exit(1)
	}
}

func init() {
	cobra.OnInitialize(initLogger)

	// Here you will define your flags and configuration settings.
	// Cobra supports persistent flags, which, if defined here,
	// will be global for your application.
	rootCmd.PersistentFlags().IntVarP(&logLevel, "log-level", "l", 0, "server log level, -4: debug, 0: info, 4: warn, 8: error")
	// Cobra also supports local flags, which will only run
	// when this action is called directly.
	// rootCmd.Flags().BoolP("toggle", "t", false, "Help message for toggle")
}

// initLogger initializes the logger for the application
func initLogger() {
	// Create a new logger with the specified log level
	defaultLogger := log.NewLogger(logLevel)
	// Set the default logger for the application
	slog.SetDefault(defaultLogger)
	// Log the start of Talos with git commit and log level information
	slog.LogAttrs(
		context.Background(),
		slog.LevelInfo,
		"Starting Talos",
		slog.String("git_commit", version.GitCommit),
		slog.Int("log_level", logLevel),
	)
}
