package cmd

import (
	"context"
	"fmt"
	"log/slog"
	"os"

	talosctx "code.devops.xiaohongshu.com/cloud-native/talos/pkg/context"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/mcp"
	"github.com/spf13/cobra"
)

// Configuration variables for the MCP server
var (
	// Type of server (sse or stdio)
	mcpServerType string
	// Port number for the server
	mcpServerPort int
	// Flag to set server as read-only
	readOnly bool
	// Interval for refreshing server data
	refreshInterval int
	// Name of the Kubernetes cluster
	clusterName string
	// Path to the Kubernetes config file
	kubeConfigPath string
)

// mcpCmd represents the mcp command
var mcpCmd = &cobra.Command{
	Use:   "mcp",
	Short: "Start the MCP (Model Control Protocol) server",
	Long: `The mcp command initiates the Model Control Protocol server.
It offers two server types: SSE (Server-Sent Events) and stdio.
This server acts as a bridge between LLM-powered agents and Kubernetes clusters,
facilitating advanced management, monitoring, and analysis capabilities.`,
	Run: func(cmd *cobra.Command, args []string) {
		// Create a new MCP server
		server, err := mcp.NewServer(readOnly, clusterName, kubeConfigPath)
		if err != nil {
			slog.LogAttrs(context.Background(), slog.LevelError,
				"Failed to create MCP server", slog.Any("error", err))
			os.Exit(1)
		}

		// Handle different server types
		switch mcpServerType {
		case "stdio":
			// Serve using standard I/O
			if err := server.ServeStdio(); err != nil {
				slog.LogAttrs(context.Background(), slog.LevelError,
					"Failed to serve stdio", slog.Any("error", err))
				os.Exit(1)
			}
		case "sse":
			// Serve using Server-Sent Events (SSE)
			sseServer := server.ServeSSE()
			talosctx.RunServer(
				context.Background(),
				// Start the server
				func() error {
					slog.LogAttrs(context.Background(), slog.LevelInfo, "Starting server",
						slog.String("server_type", "sse"),
						slog.Int("port", mcpServerPort))
					return sseServer.Start(fmt.Sprintf(":%d", mcpServerPort))
				},
				// Shutdown the server
				func(ctx context.Context) error {
					slog.LogAttrs(context.Background(), slog.LevelInfo, "Shutting down server",
						slog.String("server_type", "sse"),
						slog.Int("port", mcpServerPort))
					return sseServer.Shutdown(ctx)
				},
			)
		default:
			panic("unknown server type")
		}
	},
}

func init() {
	rootCmd.AddCommand(mcpCmd)

	// Here you will define your flags and configuration settings.

	// Cobra supports Persistent Flags which will work for this command
	// and all subcommands, e.g.:
	// mcpCmd.PersistentFlags().String("foo", "", "A help for foo")

	// Cobra supports local flags which will only run when this command
	// is called directly, e.g.:
	// mcpCmd.Flags().BoolP("toggle", "t", false, "Help message for toggle")
	mcpCmd.Flags().StringVarP(&mcpServerType, "type", "t", "sse", "Set the server type, supports sse and stdio")
	mcpCmd.Flags().IntVarP(&mcpServerPort, "port", "p", 8080, "Set the server port")
	mcpCmd.Flags().BoolVarP(&readOnly, "read-only", "", true, "Set the server as read-only")
	mcpCmd.Flags().IntVarP(&refreshInterval, "interval", "i", 3600, "Set the server refresh interval")
	mcpCmd.Flags().StringVarP(&clusterName, "cluster", "c", "", "Set the server cluster name")
	mcpCmd.Flags().StringVarP(&kubeConfigPath, "kubeconfig", "k", "", "Set the server kube config path, default in cluster config")
}
