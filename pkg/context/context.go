package context

import (
	"context"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"runtime"
	"syscall"
	"time"
)

// RunServer runs the server and handles graceful shutdown.
func RunServer(ctx context.Context, start func() error, shutdown func(ctx context.Context) error) {
	// Create a cancellable context
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	// Set up signal handling
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	// Handle signals
	SafeGo(ctx, "signal_handler", func() {
		sig := <-sigCh
		slog.LogAttrs(ctx, slog.LevelInfo, "Received signal", slog.String("type", sig.String()))
		cancel()
	})

	// Handle shutdown
	SafeGo(ctx, "shutdown_handler", func() {
		<-ctx.Done()
		shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer shutdownCancel()

		if err := shutdown(shutdownCtx); err != nil {
			slog.LogAttrs(ctx, slog.LevelError, "Error shutting down server", slog.Any("error", err))
			// Instead of panic, log the error and exit
			os.Exit(1)
		}
	})

	// Start the server
	if err := start(); err != nil {
		if err != http.ErrServerClosed {
			slog.LogAttrs(ctx, slog.LevelError, "Error starting server", slog.Any("error", err))
			os.Exit(1)
		}
	}
}

// SafeGo executes a function in a goroutine with panic recovery and optimization
func SafeGo(ctx context.Context, name string, fn func(), deferFunc ...func()) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				stack := make([]byte, 4096)
				stack = stack[:runtime.Stack(stack, false)]
				slog.LogAttrs(
					ctx, slog.LevelError,
					"Server goroutine panic recovered",
					slog.String("routine", name),
					slog.Any("error", r),
					slog.String("stack", string(stack)),
				)
			}
		}()

		// execute deferFuncs
		for _, df := range deferFunc {
			defer df()
		}

		// execute fn
		fn()
	}()
}
