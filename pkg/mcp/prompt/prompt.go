package prompt

import (
	"context"
	"fmt"
	"os"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

// DefaultPrompt defines the standard configuration for the Kubernetes cluster analysis prompt.
// It includes arguments for the cluster name and namespace, and a PromptHandlerFunc for handling initial analysis.
var DefaultPrompt = mcp.NewPrompt(
	"Kubernetes Cluster Analyzer",
	mcp.WithPromptDescription(
		"Expert system for analyzing and reporting Kubernetes cluster status and health"),
	mcp.WithArgument(
		"cluster_name",
		mcp.ArgumentDescription("The name of the Kubernetes cluster to analyze"),
	),
	mcp.WithArgument(
		"namespace",
		mcp.ArgumentDescription("The namespace scope for analysis"),
	),
)

// DefaultPromptHandlerFunc returns a PromptHandlerFunc for handling initial cluster analysis.
// It takes a clusterName parameter and returns a function that handles the initial analysis.
func DefaultPromptHandlerFunc(clusterName string) server.PromptHandlerFunc {
	return func(ctx context.Context, request mcp.GetPromptRequest) (*mcp.GetPromptResult, error) {
		// Validate the context to ensure it hasn't been cancelled or timed out
		if err := ctx.Err(); err != nil {
			return nil, fmt.Errorf("context error: %w", err)
		}

		// Initialize the cluster configuration
		var config clusterConfig

		// If no cluster name is provided, extract it from environment variables or request parameters
		if clusterName == "" {
			config = extractClusterConfig(request.Params.Arguments)
		}

		// Generate the analysis message based on the cluster configuration
		message := buildAnalysisMessage(config.clusterName, config.namespace)

		// Return the prompt result with the analysis description and message
		return &mcp.GetPromptResult{
			Description: "Kubernetes Cluster Analysis",
			Messages:    []mcp.PromptMessage{message},
		}, nil
	}
}

// clusterConfig represents the configuration for a Kubernetes cluster analysis
type clusterConfig struct {
	clusterName string // The name of the Kubernetes cluster to analyze
	namespace   string // The namespace scope for the analysis
}

// extractClusterConfig extracts the cluster configuration from the provided arguments
// It prioritizes environment variables for the cluster name and uses default values for the namespace
func extractClusterConfig(args map[string]string) clusterConfig {
	// Get cluster name with environment fallback
	clusterName := os.Getenv("CLUSTER_NAME")
	if clusterName == "" {
		if name, ok := args["cluster_name"]; ok {
			clusterName = name
		}
	}
	// Return empty config if no cluster name is found
	if clusterName == "" {
		return clusterConfig{}
	}

	// Get namespace with default fallback
	namespace := "default"
	if ns, ok := args["namespace"]; ok && ns != "" {
		namespace = ns
	}

	// Return the populated cluster configuration
	return clusterConfig{
		clusterName: clusterName,
		namespace:   namespace,
	}
}

// buildAnalysisMessage creates a PromptMessage for the initial cluster analysis
// It includes a template with information about the cluster and available analysis capabilities
func buildAnalysisMessage(clusterName, namespace string) mcp.PromptMessage {
	const messageTemplate = `Initializing cluster analysis for '%s' (namespace: '%s')...
I will help you understand your cluster's current state and health.
Available analysis capabilities:
- Cluster version and component status
- Node health and capacity
- Namespace overview
- API resources availability
- Resource utilization metrics

Executing initial cluster analysis...`

	// Create and return a new PromptMessage with the formatted template
	return mcp.NewPromptMessage(
		mcp.RoleAssistant,
		mcp.NewTextContent(fmt.Sprintf(messageTemplate, clusterName, namespace)),
	)
}
