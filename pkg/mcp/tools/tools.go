package tools

import (
	"context"
	"fmt"
	"reflect"
	"strings"

	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/mcp/kubernetes"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

// Meta defines the interface for creating tool definitions
type Meta interface {
	// CreateResource returns a tool for creating a new resource in the Kubernetes cluster
	CreateResource() mcp.Tool
	// DeleteResource returns a tool for deleting a resource from the Kubernetes cluster
	DeleteResource() mcp.Tool
	// UpdateResource returns a tool for updating an existing resource in the Kubernetes cluster
	UpdateResource() mcp.Tool
	// GetResource returns a tool for retrieving a specific resource from the Kubernetes cluster
	GetResource() mcp.Tool
	// ListResources returns a tool for listing resources in the Kubernetes cluster
	ListResources() mcp.Tool
	// GetClusterInfo returns a tool for getting general information about the Kubernetes cluster
	GetClusterInfo() mcp.Tool
	// TODO: Add tool to handle DNS lookup and nameserver connectivity and host dns check
}

// Handle defines the interface for handling tool requests
type Handle interface {
	// CreateResourceHandler returns a handler function for creating a new resource
	CreateResourceHandler() server.ToolHandlerFunc
	// DeleteResourceHandler returns a handler function for deleting a resource
	DeleteResourceHandler() server.ToolHandlerFunc
	// UpdateResourceHandler returns a handler function for updating an existing resource
	UpdateResourceHandler() server.ToolHandlerFunc
	// GetResourceHandler returns a handler function for retrieving a specific resource
	GetResourceHandler() server.ToolHandlerFunc
	// ListResourcesHandler returns a handler function for listing resources
	ListResourcesHandler() server.ToolHandlerFunc
	// GetClusterInfoHandler returns a handler function for getting cluster information
	GetClusterInfoHandler() server.ToolHandlerFunc
}

// Interface defines the combined Meta and Handle interfaces for tools
type Interface interface {
	// GetServerTools returns a slice of ServerTool, which combines Tool and Handler
	// This method is used to get all the server tools available in the implementation
	GetServerTools() ([]server.ServerTool, error)

	Meta   // Embeds the Meta interface for tool definitions
	Handle // Embeds the Handle interface for tool request handlers
}

// Ensure that tool implements Interface
var _ Interface = (*tool)(nil)

// tool is a struct that implements the Interface
type tool struct {
	readOnly bool
	client   kubernetes.Interface // Kubernetes client for interacting with the cluster
}

// New creates a new instance of the tool struct implementing the Interface
func New(readOnly bool, client kubernetes.Interface) Interface {
	return &tool{
		readOnly: readOnly,
		client:   client,
	}
}

// GetServerTools returns a slice of ServerTool, which combines Tool and Handler
func (t *tool) GetServerTools() ([]server.ServerTool, error) {
	// Initialize slices to store tools and errors
	var tools []server.ServerTool
	var errs []string

	// Get the type and value of the tool struct
	toolType := reflect.TypeOf(t)
	toolValue := reflect.ValueOf(t)

	// Iterate through all methods of the tool struct
	for i := range make([]struct{}, toolType.NumMethod()) {
		method := toolType.Method(i)

		// Only process methods that return mcp.Tool
		if method.Type.NumOut() != 1 || method.Type.Out(0) != reflect.TypeOf((*mcp.Tool)(nil)).Elem() {
			continue
		}

		// Skip methods with "Handler" suffix
		if strings.HasSuffix(method.Name, "Handler") {
			continue
		}

		// Anonymous function to handle panics and process each method
		func() {
			defer func() {
				if r := recover(); r != nil {
					errs = append(errs, fmt.Sprintf("panic while processing method %s: %v", method.Name, r))
				}
			}()

			// Get the tool definition
			toolResult := method.Func.Call([]reflect.Value{toolValue})
			if len(toolResult) != 1 {
				errs = append(errs, fmt.Sprintf("method %s: expected 1 return value, got %d", method.Name, len(toolResult)))
				return
			}

			tool, ok := toolResult[0].Interface().(mcp.Tool)
			if !ok {
				errs = append(errs, fmt.Sprintf("method %s: return value is not mcp.Tool", method.Name))
				return
			}

			// Validate tool name
			if tool.Name == "" {
				errs = append(errs, fmt.Sprintf("method %s: tool name cannot be empty", method.Name))
				return
			}

			// Find corresponding handler function
			handlerMethodName := method.Name + "Handler"
			handlerMethod := toolValue.MethodByName(handlerMethodName)

			if !handlerMethod.IsValid() {
				errs = append(errs, fmt.Sprintf("handler method %s not found for tool %s", handlerMethodName, method.Name))
				return
			}

			// Validate handler function type
			handlerType := handlerMethod.Type()
			if handlerType.NumOut() != 1 || !handlerType.Out(0).AssignableTo(reflect.TypeOf((*server.ToolHandlerFunc)(nil)).Elem()) {
				errs = append(errs, fmt.Sprintf("method %s: invalid handler signature", handlerMethodName))
				return
			}

			// Get handler function
			handlerResult := handlerMethod.Call([]reflect.Value{})
			handler, ok := handlerResult[0].Interface().(server.ToolHandlerFunc)
			if !ok {
				errs = append(errs, fmt.Sprintf("method %s: handler is not server.ToolHandlerFunc", handlerMethodName))
				return
			}

			// Validate handler function is not nil
			if reflect.ValueOf(handler).IsNil() {
				errs = append(errs, fmt.Sprintf("method %s: handler function is nil", handlerMethodName))
				return
			}

			// Add valid tool and handler to the tools slice
			tools = append(tools, server.ServerTool{
				Tool:    tool,
				Handler: handler,
			})
		}()
	}

	// If there are errors, return all collected errors
	if len(errs) > 0 {
		return nil, fmt.Errorf("errors while getting server tools:\n%s", strings.Join(errs, "\n"))
	}

	// Validate if any tools were found
	if len(tools) == 0 {
		return nil, fmt.Errorf("no valid tools found in %T", t)
	}

	// Check for uniqueness of tool names
	toolNames := make(map[string]bool)
	for _, tool := range tools {
		if toolNames[tool.Tool.Name] {
			return nil, fmt.Errorf("duplicate tool name found: %s", tool.Tool.Name)
		}
		toolNames[tool.Tool.Name] = true
	}

	return tools, nil
}

// CreateResource returns a tool for creating a new resource in the Kubernetes cluster
func (t *tool) CreateResource() mcp.Tool {
	return mcp.NewTool(
		"create_resource",
		mcp.WithDescription("Create a new Kubernetes resource"),
		mcp.WithString(
			"kind",
			mcp.Description("The type of resource to create"),
			mcp.Required(),
		),
		mcp.WithString(
			"namespace",
			mcp.Description("The namespace in which to create the resource"),
			mcp.Required(),
		),
		mcp.WithString(
			"manifest",
			mcp.Description("The JSON manifest for the resource to create"),
			mcp.Required(),
		),
	)
}

// DeleteResource returns a tool for deleting a resource from the Kubernetes cluster
func (t *tool) DeleteResource() mcp.Tool {
	return mcp.NewTool(
		"delete_resource",
		mcp.WithDescription("Delete a Kubernetes resource"),
		mcp.WithString(
			"kind",
			mcp.Description("The type of resource to delete"),
			mcp.Required(),
		),
		mcp.WithString(
			"namespace",
			mcp.Description("The namespace from which to delete the resource"),
			mcp.Required(),
		),
		mcp.WithString(
			"name",
			mcp.Description("The name of the resource to delete"),
			mcp.Required(),
		),
	)
}

// UpdateResource returns a tool for updating an existing resource in the Kubernetes cluster
func (t *tool) UpdateResource() mcp.Tool {
	return mcp.NewTool(
		"update_resource",
		mcp.WithDescription("Update an existing Kubernetes resource"),
		mcp.WithString(
			"kind",
			mcp.Description("The type of resource to update"),
			mcp.Required(),
		),
		mcp.WithString(
			"namespace",
			mcp.Description("The namespace of the resource to update"),
			mcp.Required(),
		),
		mcp.WithString(
			"manifest",
			mcp.Description("The JSON manifest for the resource to update"),
			mcp.Required(),
		),
	)
}

// GetResource returns a tool for retrieving a specific resource from the Kubernetes cluster
func (t *tool) GetResource() mcp.Tool {
	return mcp.NewTool(
		"get_resource",
		mcp.WithDescription("Get a specific Kubernetes resource"),
		mcp.WithString(
			"kind",
			mcp.Description("The type of resource to get"),
			mcp.Required(),
		),
		mcp.WithString(
			"namespace",
			mcp.Description("The namespace of the resource"),
			mcp.Required(),
		),
		mcp.WithString(
			"name",
			mcp.Description("The name of the resource to get"),
			mcp.Required(),
		),
	)
}

// ListResources returns a tool for listing resources in the Kubernetes cluster
func (t *tool) ListResources() mcp.Tool {
	return mcp.NewTool(
		"list_resources",
		mcp.WithDescription("List Kubernetes resources"),
		mcp.WithString(
			"kind",
			mcp.Description("The type of resources to list"),
			mcp.Required(),
		),
		mcp.WithString(
			"namespace",
			mcp.Description("The namespace to list resources from"),
			mcp.Required(),
		),
		mcp.WithString(
			"label_selector",
			mcp.Description("Label selector to filter resources (e.g., 'app=nginx,environment=production')"),
		),
		mcp.WithString(
			"field_selector",
			mcp.Description("Field selector to filter resources (e.g., 'status.phase=Running')"),
		),
	)
}

// GetClusterInfo returns a tool for getting general information about the Kubernetes cluster
func (t *tool) GetClusterInfo() mcp.Tool {
	return mcp.NewTool(
		"get_cluster_info",
		mcp.WithDescription("Get general information about the Kubernetes cluster, such as version, API resources, component status, and node information"),
	)
}

// CreateResourceHandler returns a handler function for creating a new resource
func (t *tool) CreateResourceHandler() server.ToolHandlerFunc {
	return func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		// Check if the server is in read-only mode
		if err := t.handleReadOnly(); err != nil {
			return nil, err
		}

		// Define parameters required for creating a resource
		params := []struct {
			name  string
			value *string
		}{
			{"kind", new(string)},
			{"namespace", new(string)},
			{"manifest", new(string)},
		}

		// Validate and extract required parameters from the request
		for _, param := range params {
			val, ok := request.Params.Arguments[param.name].(string)
			if !ok || val == "" {
				return nil, fmt.Errorf("%s is required and cannot be empty", param.name)
			}
			*param.value = val
		}

		// Call the Kubernetes client to create the resource
		byteResult, err := t.client.CreateResource(ctx, *params[0].value, *params[1].value, *params[2].value)
		if err != nil {
			return nil, fmt.Errorf("failed to create resource: %w", err)
		}

		// Return the result as a tool result text
		return mcp.NewToolResultText(string(byteResult)), nil
	}
}

// DeleteResourceHandler returns a handler function for deleting a resource
func (t *tool) DeleteResourceHandler() server.ToolHandlerFunc {
	return func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		// Check if the server is in read-only mode
		if err := t.handleReadOnly(); err != nil {
			return nil, err
		}

		// Define parameters required for deleting a resource
		params := []struct {
			name  string
			value *string
		}{
			{"kind", new(string)},
			{"namespace", new(string)},
			{"name", new(string)},
		}

		// Validate and extract required parameters from the request
		for _, param := range params {
			val, ok := request.Params.Arguments[param.name].(string)
			if !ok || val == "" {
				return nil, fmt.Errorf("%s is required and cannot be empty", param.name)
			}
			*param.value = val
		}

		// Call the Kubernetes client to delete the resource
		err := t.client.DeleteResource(ctx, *params[0].value, *params[1].value, *params[2].value)
		if err != nil {
			return nil, fmt.Errorf("failed to delete resource: %w", err)
		}

		// Return a success message
		return mcp.NewToolResultText("Resource deleted successfully"), nil
	}
}

// UpdateResourceHandler returns a handler function for updating an existing resource
func (t *tool) UpdateResourceHandler() server.ToolHandlerFunc {
	return func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		// Check if the server is in read-only mode
		if err := t.handleReadOnly(); err != nil {
			return nil, err
		}

		// Define parameters required for updating a resource
		params := []struct {
			name  string
			value *string
		}{
			{"kind", new(string)},
			{"namespace", new(string)},
			{"manifest", new(string)},
		}

		// Validate and extract required parameters from the request
		for _, param := range params {
			val, ok := request.Params.Arguments[param.name].(string)
			if !ok || val == "" {
				return nil, fmt.Errorf("%s is required and cannot be empty", param.name)
			}
			*param.value = val
		}

		// Call the Kubernetes client to update the resource
		byteResult, err := t.client.UpdateResource(ctx, *params[0].value, *params[1].value, *params[2].value)
		if err != nil {
			return nil, fmt.Errorf("failed to update resource: %w", err)
		}

		// Return the result as a tool result text
		return mcp.NewToolResultText(string(byteResult)), nil
	}
}

// GetResourceHandler returns a handler function for retrieving a specific resource
func (t *tool) GetResourceHandler() server.ToolHandlerFunc {
	return func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		// Define parameters required for retrieving a resource
		params := []struct {
			name  string
			value *string
		}{
			{"kind", new(string)},
			{"namespace", new(string)},
			{"name", new(string)},
		}

		// Validate and extract required parameters from the request
		for _, param := range params {
			val, ok := request.Params.Arguments[param.name].(string)
			if !ok || val == "" {
				return nil, fmt.Errorf("%s is required and cannot be empty", param.name)
			}
			*param.value = val
		}

		// Call the Kubernetes client to get the resource
		byteResult, err := t.client.GetResource(ctx, *params[0].value, *params[1].value, *params[2].value)
		if err != nil {
			return nil, fmt.Errorf("failed to get resource: %w", err)
		}

		// Return the result as a tool result text
		return mcp.NewToolResultText(string(byteResult)), nil
	}
}

// ListResourcesHandler returns a handler function for listing resources
func (t *tool) ListResourcesHandler() server.ToolHandlerFunc {
	return func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		// Define parameters required for listing resources
		requiredParams := []string{"kind"}
		optionalParams := []string{"namespace", "label_selector", "field_selector"}
		params := make(map[string]*string)

		// Validate and extract required parameters from the request
		for _, name := range requiredParams {
			val, ok := request.Params.Arguments[name].(string)
			if !ok || val == "" {
				return nil, fmt.Errorf("%s is required and cannot be empty", name)
			}
			params[name] = &val
		}

		// Extract optional parameters from the request
		for _, name := range optionalParams {
			if val, ok := request.Params.Arguments[name].(string); ok {
				params[name] = &val
			} else {
				params[name] = new(string)
			}
		}

		// Call the Kubernetes client to list resources
		byteResult, err := t.client.ListResources(
			ctx,
			*params["kind"],
			*params["namespace"],
			*params["label_selector"],
			*params["field_selector"],
		)
		if err != nil {
			return nil, fmt.Errorf("failed to list resources: %w", err)
		}

		// Return the result as a tool result text
		return mcp.NewToolResultText(string(byteResult)), nil
	}
}

// GetClusterInfoHandler returns a handler function for getting cluster information
func (t *tool) GetClusterInfoHandler() server.ToolHandlerFunc {
	return func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		// Call the Kubernetes client to get cluster information
		byteResult, err := t.client.GetClusterInfo(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to get cluster info: %w", err)
		}

		// Return the result as a tool result text
		return mcp.NewToolResultText(string(byteResult)), nil
	}
}

// handleReadOnly returns an error if the server is in read-only mode
func (t *tool) handleReadOnly() error {
	if t.readOnly {
		return fmt.Errorf("server is read-only, cannot perform mutating operations")
	}
	return nil
}
