package mcp

import (
	"context"
	"fmt"

	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/config"
	einoextmcp "github.com/cloudwego/eino-ext/components/tool/mcp"
	"github.com/cloudwego/eino/compose"
	"github.com/cloudwego/eino/schema"
	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
)

// NewMCPClient creates a new MCP (Model Control Protocol) client for tool access.
// It initializes an SSE-based MCP client with the provided configuration and client info.
// The client is started and initialized with the latest protocol version.
func NewMCPClient(ctx context.Context, config *config.MCPConfig, clientInfo mcp.Implementation) (*client.Client, error) {
	cli, err := client.NewSSEMCPClient(config.BaseUrl)
	if err != nil {
		return nil, err
	}

	if err := cli.Start(ctx); err != nil {
		return nil, err
	}

	initRequest := mcp.InitializeRequest{}
	initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
	initRequest.Params.ClientInfo = clientInfo
	if _, err := cli.Initialize(ctx, initRequest); err != nil {
		return nil, err
	}

	return cli, nil
}

// CallMCPTool calls a specific tool using the MCP client and returns the result as a byte array.
func CallMCPTool(ctx context.Context, cli *client.Client, toolName string, args map[string]any) (string, error) {
	request := mcp.CallToolRequest{
		Request: mcp.Request{Method: "tools/call"},
	}
	request.Params.Name = toolName
	request.Params.Arguments = args
	result, err := cli.CallTool(ctx, request)
	if err != nil {
		return "", fmt.Errorf("failed to call tool %s: %w", toolName, err)
	}

	for _, content := range result.Content {
		if textContent, ok := content.(mcp.TextContent); ok {
			return textContent.Text, nil
		}
	}

	return "", fmt.Errorf("no text content found in tool result")
}

// GetToolInfos retrieves information about the provided tools.
func GetToolInfos(ctx context.Context, config *compose.ToolsNodeConfig) ([]*schema.ToolInfo, error) {
	// Initialize a slice to store tool information
	toolInfos := make([]*schema.ToolInfo, 0, len(config.Tools))
	for _, tool := range config.Tools {
		// Get information for the current tool
		ti, err := tool.Info(ctx)
		if err != nil {
			return nil, err
		}

		// Append the tool information to the slice
		toolInfos = append(toolInfos, ti)
	}

	// Return the slice of tool information
	return toolInfos, nil
}

// GetToolsMetadata retrieves tool metadata and configuration for the Telemeter
func GetToolsMetadata(ctx context.Context, client client.MCPClient) ([]*schema.ToolInfo, *compose.ToolsNodeConfig, error) {
	// Get base tools from the MCP client
	baseTools, err := einoextmcp.GetTools(ctx, &einoextmcp.Config{Cli: client})
	if err != nil {
		return nil, nil, err
	}

	// Create a ToolsNodeConfig with the retrieved base tools
	toolsNodeConfig := &compose.ToolsNodeConfig{Tools: baseTools}

	// Get tool information using the ToolsNodeConfig
	toolInfos, err := GetToolInfos(ctx, toolsNodeConfig)
	if err != nil {
		return nil, nil, err
	}

	// Return the tool information and configuration
	return toolInfos, toolsNodeConfig, nil
}
