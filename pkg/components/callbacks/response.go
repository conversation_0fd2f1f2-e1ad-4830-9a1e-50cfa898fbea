package callbacks

import (
	"context"
	"errors"
	"io"
	"log/slog"
	"sync"

	"github.com/cloudwego/eino-ext/components/model/deepseek"
	"github.com/cloudwego/eino/callbacks"
	"github.com/cloudwego/eino/components/model"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
	callbackutils "github.com/cloudwego/eino/utils/callbacks"
	"github.com/gin-gonic/gin"
)

// MessageType represents the type of message
type MessageType string

const (
	// Meta represents the meta message	a
	Meta MessageType = "meta"
	// Think represents the think message
	Think MessageType = "think"
	// Answer represents the answer message
	Answer MessageType = "answer"
)

type Message struct {
	// Type represents the type of message
	Type MessageType `json:"type"`
	// Content represents the content of message
	Content string `json:"content"`
}

type ResponseHandler struct {
	// ch represents the channel of message
	ch chan Message
	// currantAgentName represents the current agent name
	currantAgentName string
	// agentReasoning represents the agent reasoning
	agentReasoning map[string]bool
	// mu represents the mutex
	mu sync.Mutex
	// wg represents the wait group
	wg sync.WaitGroup
}

// DefaultResponseHandler is the default response handler
var DefaultResponseHandler = NewResponseHandler()

// NewResponseHandler creates a new response handler
func NewResponseHandler() *ResponseHandler {
	return &ResponseHandler{
		ch: make(chan Message),
		agentReasoning: map[string]bool{
			"planner": false,
			"doer":    false,
			"reviser": false,
		},
	}
}

// Stream sends messages to the client
func (r *ResponseHandler) Stream(c *gin.Context) {
	go func() {
		for msg := range r.ch {
			switch msg.Type {
			case Meta:
				c.SSEvent("meta", msg.Content)
			case Think:
				c.SSEvent("think", msg.Content)
			case Answer:
				c.SSEvent("answer", msg.Content)
			}
		}
	}()
}

// CloseChannel closes the channel
func (r *ResponseHandler) CloseChannel() {
	close(r.ch)
}

// WaitStreamOutput waits for the stream output to be closed
func (r *ResponseHandler) WaitStreamOutput() {
	r.wg.Wait()
}

// CallBackHandler returns a callback handler
func (r *ResponseHandler) CallBackHandler() callbacks.Handler {
	return callbackutils.NewHandlerHelper().
		ChatModel(&callbackutils.ModelCallbackHandler{
			OnEndWithStreamOutput: r.onChatModelEndWithStreamOutput,
		}).
		Tool(&callbackutils.ToolCallbackHandler{
			OnEndWithStreamOutput: r.onToolEndWithStreamOutput,
		}).
		Handler()
}

// onChatModelEndWithStreamOutput handles the end of a chat model stream
func (r *ResponseHandler) onChatModelEndWithStreamOutput(ctx context.Context, info *callbacks.RunInfo, output *schema.StreamReader[*model.CallbackOutput]) context.Context {
	name := info.Name
	if name != r.currantAgentName {
		r.ch <- Message{
			Type:    Meta,
			Content: name,
		}
		r.currantAgentName = name
	}

	r.wg.Add(1)

	go func() {
		defer output.Close()
		defer r.wg.Done()

		for {
			chunk, err := output.Recv()
			if errors.Is(err, io.EOF) {
				break
			}
			if err != nil {
				slog.Error("Failed to receive chunk", "error", err)
				return
			}

			if len(chunk.Message.Content) > 0 {
				if r.agentReasoning[name] {
					r.ch <- Message{
						Type:    Meta,
						Content: "开始回答: \n",
					}
					r.mu.Lock()
					r.agentReasoning[name] = false
					r.mu.Unlock()
				}
				r.ch <- Message{
					Type:    Answer,
					Content: chunk.Message.Content,
				}
			} else if reasoningContent, ok := deepseek.GetReasoningContent(chunk.Message); ok {
				if !r.agentReasoning[name] {
					r.ch <- Message{
						Type:    Meta,
						Content: "开始思考: \n",
					}
					r.mu.Lock()
					r.agentReasoning[name] = true
					r.mu.Unlock()
				}
				r.ch <- Message{
					Type:    Think,
					Content: reasoningContent,
				}
			}
		}
	}()
	return ctx
}

// onToolEndWithStreamOutput handles the end of a tool stream
func (r *ResponseHandler) onToolEndWithStreamOutput(ctx context.Context, info *callbacks.RunInfo, output *schema.StreamReader[*tool.CallbackOutput]) context.Context {
	return ctx
}
