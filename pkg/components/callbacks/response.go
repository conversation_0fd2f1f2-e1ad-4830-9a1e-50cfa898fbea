package callbacks

import (
	"context"
	"errors"
	"io"
	"log/slog"
	"sync"
	"time"

	"github.com/cloudwego/eino-ext/components/model/deepseek"
	"github.com/cloudwego/eino/callbacks"
	"github.com/cloudwego/eino/components/model"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
	callbackutils "github.com/cloudwego/eino/utils/callbacks"
	"github.com/gin-gonic/gin"
)

// MessageType represents the type of message
type MessageType string

const (
	// Meta represents the meta message	a
	Meta MessageType = "meta"
	// Think represents the think message
	Think MessageType = "think"
	// Answer represents the answer message
	Answer MessageType = "answer"
)

type Message struct {
	// Type represents the type of message
	Type MessageType `json:"type"`
	// Content represents the content of message
	Content string `json:"content"`
}

type ResponseHandler struct {
	// ch represents the channel of message
	ch chan Message
	// currentAgentName represents the current agent name
	currentAgentName string
	// agentReasoning represents the agent reasoning
	agentReasoning map[string]bool
	// mu represents the mutex
	mu sync.Mutex
	// wg represents the wait group
	wg sync.WaitGroup
	// closed indicates if the handler is closed
	closed bool
}

// NewResponseHandler creates a new response handler
func NewResponseHandler() *ResponseHandler {
	return &ResponseHandler{
		ch: make(chan Message, 100), // 使用缓冲通道防止阻塞
		agentReasoning: map[string]bool{
			"planner": false,
			"doer":    false,
			"reviser": false,
		},
	}
}

// GetChannel 返回消息通道（只读）
func (r *ResponseHandler) GetChannel() <-chan Message {
	return r.ch
}

// Stream sends messages to the client
func (r *ResponseHandler) Stream(c *gin.Context) {
	go func() {
		for msg := range r.ch {
			switch msg.Type {
			case Meta:
				c.SSEvent("meta", msg.Content)
			case Think:
				c.SSEvent("think", msg.Content)
			case Answer:
				c.SSEvent("answer", msg.Content)
			}
		}
	}()
}

// CloseChannel 安全关闭通道
func (r *ResponseHandler) CloseChannel() {
	r.mu.Lock()
	defer r.mu.Unlock()

	if !r.closed {
		r.closed = true
		close(r.ch)
		slog.Info("响应处理器通道已关闭")
	}
}

// WaitStreamOutput waits for the stream output to be closed
func (r *ResponseHandler) WaitStreamOutput() {
	r.wg.Wait()
}

// CallBackHandler returns a callback handler
func (r *ResponseHandler) CallBackHandler() callbacks.Handler {
	return callbackutils.NewHandlerHelper().
		ChatModel(&callbackutils.ModelCallbackHandler{
			OnEndWithStreamOutput: r.onChatModelEndWithStreamOutput,
		}).
		Tool(&callbackutils.ToolCallbackHandler{
			OnEndWithStreamOutput: r.onToolEndWithStreamOutput,
		}).
		Handler()
}

// onChatModelEndWithStreamOutput handles the end of a chat model stream
func (r *ResponseHandler) onChatModelEndWithStreamOutput(ctx context.Context, info *callbacks.RunInfo, output *schema.StreamReader[*model.CallbackOutput]) context.Context {
	name := info.Name
	if name != r.currentAgentName {
		select {
		case r.ch <- Message{
			Type:    Meta,
			Content: name,
		}:
		case <-ctx.Done():
			slog.Warn("上下文已取消，跳过代理名称消息", "agent", name)
			return ctx
		default:
			slog.Warn("通道已满，跳过代理名称消息", "agent", name)
		}
		r.currentAgentName = name
	}

	r.wg.Add(1)

	go func() {
		defer output.Close()
		defer r.wg.Done()

		// 设置处理超时
		timeout := time.NewTimer(5 * time.Minute)
		defer timeout.Stop()

		for {
			select {
			case <-ctx.Done():
				slog.Info("上下文已取消，停止聊天模型流处理", "agent", name)
				return
			case <-timeout.C:
				slog.Warn("聊天模型流处理超时", "agent", name)
				return
			default:
				chunk, err := output.Recv()
				if errors.Is(err, io.EOF) {
					return
				}
				if err != nil {
					slog.Error("接收消息块失败", "error", err, "agent", name)
					return
				}

				if len(chunk.Message.Content) > 0 {
					if r.agentReasoning[name] {
						select {
						case r.ch <- Message{
							Type:    Meta,
							Content: "开始回答: \n",
						}:
						case <-ctx.Done():
							return
						default:
							slog.Warn("通道已满，跳过元数据消息")
						}
						r.mu.Lock()
						r.agentReasoning[name] = false
						r.mu.Unlock()
					}

					select {
					case r.ch <- Message{
						Type:    Answer,
						Content: chunk.Message.Content,
					}:
					case <-ctx.Done():
						return
					default:
						slog.Warn("通道已满，跳过回答消息")
					}
				} else if reasoningContent, ok := deepseek.GetReasoningContent(chunk.Message); ok {
					if !r.agentReasoning[name] {
						select {
						case r.ch <- Message{
							Type:    Meta,
							Content: "开始思考: \n",
						}:
						case <-ctx.Done():
							return
						default:
							slog.Warn("通道已满，跳过思考开始消息")
						}
						r.mu.Lock()
						r.agentReasoning[name] = true
						r.mu.Unlock()
					}

					select {
					case r.ch <- Message{
						Type:    Think,
						Content: reasoningContent,
					}:
					case <-ctx.Done():
						return
					default:
						slog.Warn("通道已满，跳过思考内容消息")
					}
				}

				// 重置超时
				if !timeout.Stop() {
					<-timeout.C
				}
				timeout.Reset(5 * time.Minute)
			}
		}
	}()
	return ctx
}

// onToolEndWithStreamOutput handles the end of a tool stream
func (r *ResponseHandler) onToolEndWithStreamOutput(ctx context.Context, info *callbacks.RunInfo, output *schema.StreamReader[*tool.CallbackOutput]) context.Context {
	return ctx
}
