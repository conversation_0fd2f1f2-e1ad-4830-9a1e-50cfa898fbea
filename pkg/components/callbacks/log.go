package callbacks

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"sync"

	"github.com/cloudwego/eino-ext/components/model/deepseek"
	"github.com/cloudwego/eino/callbacks"
	"github.com/cloudwego/eino/components/model"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
	callbackutils "github.com/cloudwego/eino/utils/callbacks"
)

type coloredString struct {
	str       string
	colorCode string
}

// LogHandler is a callback handler that logs the output.
type LogHandler struct {
	ch               chan coloredString
	currentAgentName string
	agentReasoning   map[string]bool
	mu               sync.Mutex
	wg               sync.WaitGroup
	closed           bool
}

// NewLogHandler creates a new LogHandler.
func NewLogHandler() *LogHandler {
	return &LogHandler{
		ch: make(chan coloredString, 100), // 使用缓冲通道防止阻塞
		agentReasoning: map[string]bool{
			"planner": false,
			"doer":    false,
			"reviser": false,
		},
	}
}

const (
	Reset  = "\033[0m"
	Red    = "\033[31m"
	Green  = "\033[32m"
	Yellow = "\033[33m"
	Blue   = "\033[34m"
	White  = "\033[97m"
	Cyan   = "\033[36m"
	Gray   = "\033[37m"
)

// PrintStreamOutput prints the stream output.
func (l *LogHandler) PrintStreamOutput() {
	go func() {
		for msg := range l.ch {
			fmt.Print(msg.colorCode + msg.str + Reset)
		}
	}()
}

// CloseChannels 安全关闭所有通道
func (l *LogHandler) CloseChannels() {
	l.mu.Lock()
	defer l.mu.Unlock()

	if !l.closed {
		l.closed = true
		close(l.ch)
		slog.Info("日志处理器通道已关闭")
	}
}

// WaitStreamOutput waits for the stream output to be closed.
func (l *LogHandler) WaitStreamOutput() {
	l.wg.Wait()
}

// CallBackHandler returns a callback handler.
func (l *LogHandler) CallBackHandler() callbacks.Handler {
	return callbackutils.NewHandlerHelper().
		ChatModel(&callbackutils.ModelCallbackHandler{
			OnEndWithStreamOutput: l.onChatModelEndWithStreamOutput,
		}).
		Tool(&callbackutils.ToolCallbackHandler{
			OnEndWithStreamOutput: l.onToolEndWithStreamOutput,
		}).
		Handler()
}

// onChatModelEndWithStreamOutput handles the end of a chat model stream.
func (l *LogHandler) onChatModelEndWithStreamOutput(ctx context.Context, info *callbacks.RunInfo, output *schema.StreamReader[*model.CallbackOutput]) context.Context {
	name := info.Name
	if name != l.currentAgentName {
		l.ch <- coloredString{
			str:       fmt.Sprintf("\n\n========== %s ==========\n", name),
			colorCode: Cyan,
		}
		l.currentAgentName = name
	}

	l.wg.Add(1)

	go func() {
		defer output.Close()
		defer l.wg.Done()

		for {
			chunk, err := output.Recv()
			if errors.Is(err, io.EOF) {
				break
			}
			if err != nil {
				slog.Error("Failed to receive chunk", "error", err)
				return
			}

			if len(chunk.Message.Content) > 0 {
				if l.agentReasoning[name] {
					l.ch <- coloredString{
						str:       "\n开始回答: \n",
						colorCode: Green,
					}
					l.mu.Lock()
					l.agentReasoning[name] = false
					l.mu.Unlock()
				}
				l.ch <- coloredString{
					str:       chunk.Message.Content,
					colorCode: Yellow,
				}
			} else if reasoningContent, ok := deepseek.GetReasoningContent(chunk.Message); ok {
				if !l.agentReasoning[name] {
					l.ch <- coloredString{
						str:       "\n开始思考: \n",
						colorCode: Green,
					}
					l.mu.Lock()
					l.agentReasoning[name] = true
					l.mu.Unlock()
				}
				l.ch <- coloredString{
					str:       reasoningContent,
					colorCode: White,
				}
			}
		}
	}()

	return ctx
}

// onToolEnd handles the end of a tool.
func (l *LogHandler) onToolEndWithStreamOutput(ctx context.Context, info *callbacks.RunInfo, output *schema.StreamReader[*tool.CallbackOutput]) context.Context {
	name := info.Name
	l.ch <- coloredString{
		str:       fmt.Sprintf("\n%s 开始调用工具: \n", name),
		colorCode: Blue,
	}

	l.wg.Add(1)

	go func() {
		defer output.Close()
		defer l.wg.Done()

		for {
			chunk, err := output.Recv()
			if errors.Is(err, io.EOF) {
				break
			}
			if err != nil {
				slog.Error("Failed to receive chunk", "error", err)
				return
			}

			response := make(map[string]any)
			if err := json.Unmarshal([]byte(chunk.Response), &response); err != nil {
				l.ch <- coloredString{
					str:       fmt.Sprintf("\n%s 调用工具失败: %s\n", info.Name, chunk.Response),
					colorCode: Blue,
				}
				return
			}

			formatted, err := json.MarshalIndent(response, "  ", "  ")
			if err != nil {
				l.ch <- coloredString{
					str:       fmt.Sprintf("\n%s 调用工具失败: %s\n", info.Name, chunk.Response),
					colorCode: Blue,
				}
				return
			}

			l.ch <- coloredString{
				str:       fmt.Sprintf("\n%s 调用工具成功: %s\n", info.Name, string(formatted)),
				colorCode: Blue,
			}
		}
	}()

	return ctx
}
