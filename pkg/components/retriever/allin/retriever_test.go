package allin

import (
	"context"
	"net/http"
	"testing"
	"time"

	"github.com/cloudwego/eino/components/retriever"
	"github.com/cloudwego/eino/schema"
)

func TestRetriever_Retrieve(t *testing.T) {
	type fields struct {
		config *RetrieverConfig
		client *http.Client
	}
	type args struct {
		ctx   context.Context
		query string
		opts  []retriever.Option
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantDocs []*schema.Document
		wantErr  bool
	}{
		{
			name: "successful retrieval",
			fields: fields{
				config: &RetrieverConfig{
					Endpoint: "https://aiplat-gateway.devops.beta.xiaohongshu.com",
					APPKey:   "xxx",
					APPID:    "knowledge4talos",
					Timeout:  30 * time.Second,
				},
				client: &http.Client{},
			},
			args: args{
				ctx:   context.Background(),
				query: "test query",
				opts:  []retriever.Option{},
			},
			wantDocs: []*schema.Document{},
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &Retriever{
				config: tt.fields.config,
				client: tt.fields.client,
			}
			_, err := r.Retrieve(tt.args.ctx, tt.args.query, tt.args.opts...)
			if (err != nil) != tt.wantErr {
				t.Errorf("Retrieve() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
