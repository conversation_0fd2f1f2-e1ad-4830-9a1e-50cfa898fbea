# 请求示例

curl --request POST \
  --url <https://aiplat-gateway.devops.beta.xiaohongshu.com/allin-workflow-knowledge4talos/pipelines/main> \
  --header 'content-type: application/json' \
  --header 'APP_ID: knowledge4talos' \
  --header 'APP_KEY: xxx' \
  --data '{"query": "自建集群新建容器网络插件"}'

# 响应示例

{
  "replies": [
    {
      "role": "system",
      "meta": {},
      "name": null,
      "content": [
        {
          "text": "可以参考以下信息进行回答：\n\n<知识库>\n# [0530] 容器网络监控和告警\n\n## 监控\n\n| 编号 | 名称 | 描述 | 链接 | 备注 |\n| ---- | ---- | ---- | ---- | ---- |\n| 2 | 容器网络（自建 IDC） | 1. 各集群中网络状态异常（NetworkUnavailable）的节点<br/>2. 特定节点收发和错误数据及数据包<br/>3. 特定 Pod 收发和错误数据及数据包<br/>4. 特定容器 TCP/UDP 连接数统计 | [Grafana](https://monitor.devops.xiaohongshu.com/d/PmIsiIySz/rong-qi-wang-luo-zi-jian-idc) | 缺失数据：<br/>- container_network_tcp_usage_total<br/>- container_network_tcp6_usage_total<br/>- container_network_udp_usage_total<br/>- container_network_udp6_usage_total |\n</知识库>\n\n<知识库>\n# [0516] 自建 SIT 网络打通\n\n## 集群现状\n\n| 集群名 | 集群 ID | 描述 | 物理节点 | 容器网段 | 备注 |\n| ---- | ---- | ---- | ---- | ---- | ---- |\n</知识库>\n\n<知识库>\n# [0530] 容器网络监控和告警\n\n## 监控\n\n| 编号 | 名称 | 描述 | 链接 | 备注 |\n| ---- | ---- | ---- | ---- | ---- |\n| 1 | 容器网络（云厂商） | 1. 各集群中网络状态异常（NetworkUnavailable）的节点<br/>2. 特定节点收发和错误数据及数据包<br/>3. 特定 Pod 收发和错误数据及数据包<br/>4. 特定容器 TCP/UDP 连接数统计 | [Grafana](https://monitor.devops.xiaohongshu.com/d/4H_cnRJIz/rong-qi-wang-luo?orgId=13) | 缺失数据：<br/>- container_network_tcp_usage_total<br/>- container_network_tcp6_usage_total<br/>- container_network_udp_usage_total<br/>- container_network_udp6_usage_total |\n</知识库>\n"
        }
      ]
    },
    {
      "role": "user",
      "meta": {},
      "name": null,
      "content": [
        {
          "text": "用户的问题是: 自建集群新建容器网络插件"
        }
      ]
    }
  ]
}
