package allin

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/cloudwego/eino/callbacks"
	"github.com/cloudwego/eino/components"
	"github.com/cloudwego/eino/components/retriever"
	"github.com/cloudwego/eino/schema"
)

// RetrieverConfig holds the configuration for the Allin retriever.
type RetrieverConfig struct {
	// Base URL for the API endpoint
	Endpoint string
	// APP key for authentication
	APPKey string
	// ID of the knowledge base to use (used in URL path and APP_ID header)
	APPID string
	// Timeout for API requests
	Timeout time.Duration
}

// Ensure Retriever implements the Retriever interface.
var _ retriever.Retriever = (*Retriever)(nil)

// Retriever implements the Allin retriever.
type Retriever struct {
	// config holds the configuration for the retriever
	config *RetrieverConfig
	// client is the HTTP client used for making API requests
	client *http.Client
}

// NewRetriever creates a new Allin retriever instance.
func NewRetriever(ctx context.Context, config *RetrieverConfig) (*Retriever, error) {
	// Validate the configuration
	if config == nil {
		return nil, fmt.Errorf("config is required")
	}
	if config.Endpoint == "" {
		return nil, fmt.Errorf("endpoint is required")
	}
	if config.APPKey == "" {
		return nil, fmt.Errorf("app key is required")
	}
	if config.APPID == "" {
		return nil, fmt.Errorf("app id is required")
	}

	// Set default values if not provided
	if config.Timeout <= 0 {
		// Default timeout of 30 seconds
		config.Timeout = 30 * time.Second
	}

	// Create an HTTP client with the specified timeout
	client := &http.Client{
		Timeout: config.Timeout,
	}

	// Return a new Retriever instance
	return &Retriever{
		config: config,
		client: client,
	}, nil
}

// Retrieve performs knowledge retrieval based on the input query and options.
func (r *Retriever) Retrieve(ctx context.Context, query string, opts ...retriever.Option) (docs []*schema.Document, err error) {
	// Initialize base options and apply custom options
	baseOptions := &retriever.Options{}
	options := retriever.GetCommonOptions(baseOptions, opts...)

	// Ensure run info is added to the context
	ctx = callbacks.EnsureRunInfo(ctx, r.GetType(), components.ComponentOfRetriever)

	// Prepare callback input
	callbackInput := &retriever.CallbackInput{
		Query:          query,
		ScoreThreshold: options.ScoreThreshold,
	}

	// Trigger OnStart callback
	ctx = callbacks.OnStart(ctx, callbackInput)

	// Ensure OnError callback is triggered if an error occurs
	defer func() {
		if err != nil {
			ctx = callbacks.OnError(ctx, err)
		}
	}()

	// Build the request
	req := r.buildRequest(query)

	// Perform the API request
	resp, err := r.doRequest(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve documents: %w", err)
	}

	// Convert API response to Document structs
	docs = r.convertToDocuments(resp)

	// Trigger OnEnd callback
	callbackOutput := &retriever.CallbackOutput{
		Docs: docs,
	}
	callbacks.OnEnd(ctx, callbackOutput)

	return docs, nil
}

// GetType returns the type of the retriever
func (r *Retriever) GetType() string {
	return "Allin"
}

// IsCallbacksEnabled returns whether callbacks are enabled for this retriever
func (r *Retriever) IsCallbacksEnabled() bool {
	return true
}
