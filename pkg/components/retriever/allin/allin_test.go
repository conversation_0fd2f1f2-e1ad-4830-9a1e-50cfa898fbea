package allin

import (
	"context"
	"net/http"
	"reflect"
	"testing"

	"github.com/cloudwego/eino/schema"
)

func TestRetriever_doRequest(t *testing.T) {
	type fields struct {
		config *RetrieverConfig
		client *http.Client
	}
	type args struct {
		ctx context.Context
		req *Request
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *Response
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &Retriever{
				config: tt.fields.config,
				client: tt.fields.client,
			}
			got, err := r.doRequest(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("Retriever.doRequest() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Retriever.doRequest() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_parseKnowledgeText(t *testing.T) {
	type args struct {
		text string
	}
	tests := []struct {
		name string
		args args
		want []*schema.Document
	}{
		// TODO: Add test cases.
		{
			name: "Single knowledge block",
			args: args{
				text: "可以参考以下信息进行回答：\n\n<知识库>\n# [0530] 容器网络监控和告警\n\n## 监控\n\n| 编号 | 名称 | 描述 | 链接 | 备注 |\n| ---- | ---- | ---- | ---- | ---- |\n| 2 | 容器网络（自建 IDC） | 1. 各集群中网络状态异常（NetworkUnavailable）的节点\n2. 特定节点收发和错误数据及数据包\n3. 特定 Pod 收发和错误数据及数据包\n4. 特定容器 TCP/UDP 连接数统计 | [Grafana](https://monitor.devops.xiaohongshu.com/d/PmIsiIySz/rong-qi-wang-luo-zi-jian-idc) | 缺失数据：<br/>- container_network_tcp_usage_total<br/>- container_network_tcp6_usage_total<br/>- container_network_udp_usage_total<br/>- container_network_udp6_usage_total |\n</知识库>",
			},
			want: []*schema.Document{
				{
					ID:       "doc-be2169c1-0",
					Content:  "\n# [0530] 容器网络监控和告警\n\n## 监控\n\n| 编号 | 名称 | 描述 | 链接 | 备注 |\n| ---- | ---- | ---- | ---- | ---- |\n| 2 | 容器网络（自建 IDC） | 1. 各集群中网络状态异常（NetworkUnavailable）的节点\n2. 特定节点收发和错误数据及数据包\n3. 特定 Pod 收发和错误数据及数据包\n4. 特定容器 TCP/UDP 连接数统计 | [Grafana](https://monitor.devops.xiaohongshu.com/d/PmIsiIySz/rong-qi-wang-luo-zi-jian-idc) | 缺失数据：<br/>- container_network_tcp_usage_total<br/>- container_network_tcp6_usage_total<br/>- container_network_udp_usage_total<br/>- container_network_udp6_usage_total |\n",
					MetaData: map[string]any{},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := parseKnowledgeText(tt.args.text); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("parseKnowledgeText() = %v, want %v", got, tt.want)
			}
		})
	}
}
