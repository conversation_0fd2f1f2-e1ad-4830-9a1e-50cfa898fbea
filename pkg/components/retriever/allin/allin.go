package allin

import (
	"bytes"
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"io"
	"maps"
	"net/http"
	"regexp"
	"strings"

	"github.com/cloudwego/eino/schema"
)

// Request represents the structure of the API request
type Request struct {
	// The search query
	Query string `json:"query"`
}

// Response represents the structure of the API response
type Response struct {
	// Array of replies
	Replies []Reply `json:"replies"`
}

// buildRequest creates a new Request object based on the given query
func (r *Retriever) buildRequest(query string) *Request {
	return &Request{Query: query}
}

// doRequest sends an HTTP request to the API and returns the response
func (r *Retriever) doRequest(ctx context.Context, req *Request) (*Response, error) {
	// Marshal the request body to JSON
	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create a new HTTP request with the given context
	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		buildURL(r.config),
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set the necessary headers
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("APP_ID", r.config.APPID)
	httpReq.Header.Set("APP_KEY", r.config.APPKey)

	// Send the HTTP request
	httpResp, err := r.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer httpResp.Body.Close()

	// Check if the response status is not OK
	if httpResp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(httpResp.Body)
		return nil, fmt.Errorf("request failed with status %d: %s", httpResp.StatusCode, string(body))
	}

	// Decode the response body into the Response struct
	var resp Response
	if err := json.NewDecoder(httpResp.Body).Decode(&resp); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	// Return the response
	return &resp, nil
}

// buildURL constructs the URL for the API request
func buildURL(config *RetrieverConfig) string {
	// Format the URL using the Endpoint and APPID from the config
	return fmt.Sprintf("%s/allin-workflow-%s/pipelines/main",
		config.Endpoint,
		config.APPID,
	)
}

// Reply represents a single reply in the response
type Reply struct {
	// Role of the reply (system, user, etc.)
	Role string `json:"role"`
	// Metadata for the reply
	Meta map[string]any `json:"meta"`
	// Name of the reply sender (optional)
	Name *string `json:"name"`
	// Content of the reply
	Content []Content `json:"content"`
}

// Content represents a piece of content in a reply
type Content struct {
	// Text content
	Text string `json:"text"`
}

// ToDocuments converts a Reply to a slice of schema.Document
func (r *Reply) ToDocuments() []*schema.Document {
	// Initialize a slice to store the converted documents
	docs := make([]*schema.Document, 0, len(r.Content))

	// Skip non-system replies
	if r.Role != string(schema.System) {
		return docs
	}

	// Iterate through each content piece in the reply
	for _, content := range r.Content {
		// Skip empty content
		if content.Text == "" {
			continue
		}

		// Parse knowledge text and convert to documents
		knowledgeDocs := parseKnowledgeText(content.Text)
		if len(knowledgeDocs) > 0 {
			// Iterate through each document in the knowledgeDocs slice
			for _, doc := range knowledgeDocs {
				// Add metadata if available
				if r.Meta != nil {
					maps.Copy(doc.MetaData, r.Meta)
				}
				// Add role to metadata
				doc.MetaData["role"] = r.Role
			}
			// Append the document to the slice
			docs = append(docs, knowledgeDocs...)
		} else {
			// Create a new Document for the content text
			doc := &schema.Document{
				ID:       generateDocumentID(content.Text, 0),
				Content:  content.Text,
				MetaData: make(map[string]any),
			}
			// Add metadata if available
			if r.Meta != nil {
				maps.Copy(doc.MetaData, r.Meta)
			}
			// Add role to metadata
			doc.MetaData["role"] = r.Role
			// Append the document to the slice
			docs = append(docs, doc)
		}
	}

	// Return the slice of converted documents
	return docs
}

// Example text:
// 可以参考以下信息进行回答：
//
// <知识库>
// # [0530] 容器网络监控和告警
//
// ## 监控
//
// | 编号 | 名称 | 描述 | 链接 | 备注 |
// | ---- | ---- | ---- | ---- | ---- |
// | 2 | 容器网络（自建 IDC） | 1. 各集群中网络状态异常（NetworkUnavailable）的节点<br/>2. 特定节点收发和错误数据及数据包<br/>3. 特定 Pod 收发和错误数据及数据包<br/>4. 特定容器 TCP/UDP 连接数统计 | [Grafana](https://monitor.devops.xiaohongshu.com/d/PmIsiIySz/rong-qi-wang-luo-zi-jian-idc) | 缺失数据：<br/>- container_network_tcp_usage_total<br/>- container_network_tcp6_usage_total<br/>- container_network_udp_usage_total<br/>- container_network_udp6_usage_total |
// </知识库>
//
// <知识库>
// # [0516] 自建 SIT 网络打通
//
// ## 集群现状
//
// | 集群名 | 集群 ID | 描述 | 物理节点 | 容器网段 | 备注 |
// | ---- | ---- | ---- | ---- | ---- | ---- |
// </知识库>
//
// <知识库>
// # [0530] 容器网络监控和告警
//
// ## 监控
//
// | 编号 | 名称 | 描述 | 链接 | 备注 |
// | ---- | ---- | ---- | ---- | ---- |
// | 1 | 容器网络（云厂商） | 1. 各集群中网络状态异常（NetworkUnavailable）的节点<br/>2. 特定节点收发和错误数据及数据包<br/>3. 特定 Pod 收发和错误数据及数据包<br/>4. 特定容器 TCP/UDP 连接数统计 | [Grafana](https://monitor.devops.xiaohongshu.com/d/4H_cnRJIz/rong-qi-wang-luo?orgId=13) | 缺失数据：<br/>- container_network_tcp_usage_total<br/>- container_network_tcp6_usage_total<br/>- container_network_udp_usage_total<br/>- container_network_udp6_usage_total |
// </知识库>
//
// parseKnowledgeText extracts knowledge blocks from the input text and converts them to documents
func parseKnowledgeText(text string) []*schema.Document {
	// Extract knowledge blocks from the input text
	blocks := extractKnowledgeBlocks(text)
	// Convert the extracted blocks into schema.Document objects
	return convertKnowledgeBlocksToDocuments(blocks)
}

// extractKnowledgeBlocks finds and extracts content between <知识库> tags in the input text
func extractKnowledgeBlocks(text string) []string {
	// Compile a regular expression to match content between <知识库> tags
	re := regexp.MustCompile(`<知识库>([\s\S]*?)</知识库>`)

	// Find all matches in the input text
	subMatches := re.FindAllStringSubmatch(text, -1)

	// Initialize a slice to store the extracted blocks
	blocks := make([]string, 0, len(subMatches))

	// Iterate through the matches
	for _, match := range subMatches {
		// Check if the match has at least two elements (full match and captured group)
		if len(match) >= 2 {
			// Trim whitespace from the captured content
			content := strings.TrimSpace(match[1])

			// If the content is not empty, add it to the blocks slice
			if content != "" {
				blocks = append(blocks, match[1])
			}
		}
	}

	// Return the extracted blocks
	return blocks
}

// convertKnowledgeBlocksToDocuments transforms the extracted knowledge blocks into schema.Document objects
func convertKnowledgeBlocksToDocuments(blocks []string) []*schema.Document {
	// Initialize a slice to store the converted documents
	docs := make([]*schema.Document, 0, len(blocks))

	// Iterate through each block
	for i, block := range blocks {
		// Create a new Document for each block
		doc := &schema.Document{
			// Generate a unique ID for the document
			ID: generateDocumentID(block, i),
			// Set the content of the document to the block text
			Content: block,
			// Initialize an empty metadata map
			MetaData: make(map[string]any),
		}
		// Append the new document to the slice
		docs = append(docs, doc)
	}

	// Return the slice of converted documents
	return docs
}

// generateDocumentID generates a unique document ID based on content and index
func generateDocumentID(content string, index int) string {
	contentHash := fmt.Sprintf("%x", sha256.Sum256([]byte(content)))[:8]
	return fmt.Sprintf("doc-%s-%d", contentHash, index)
}

// convertToDocuments converts the API response to a slice of schema.Document
func (r *Retriever) convertToDocuments(resp *Response) []*schema.Document {
	// Initialize a slice to store the converted documents
	docs := make([]*schema.Document, 0)

	// Iterate through each reply in the response
	for _, reply := range resp.Replies {
		// Convert the reply to documents and append them to the result
		replyDocs := reply.ToDocuments()
		if len(replyDocs) == 0 {
			continue
		}
		docs = append(docs, replyDocs...)
	}

	return docs
}
