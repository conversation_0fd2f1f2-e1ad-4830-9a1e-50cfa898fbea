# Allin Retriever

Allin Retriever 是一个基于 eino 框架的知识库检索组件，用于连接和查询 Allin 知识库服务。该组件实现了 eino 的 `retriever.Retriever` 接口，提供了简单易用的 API 来执行知识检索操作。

## 功能特点

- 支持基于查询的知识库检索
- 完整的错误处理和超时控制
- 支持 eino 回调机制
- 可扩展的元数据处理

## 安装

```bash
go get github.com/cloudwego/eino
```

## 配置参数

Allin Retriever 通过 `RetrieverConfig` 结构体进行配置：

| 参数 | 类型 | 描述 | 必填 |
|------|------|------|------|
| Endpoint | string | API 端点的基础 URL | 是 |
| APPKey | string | 用于认证的 APP 密钥 | 是 |
| APPID | string | 要查询的知识库 ID（用于 URL 路径和 APP_ID 头） | 是 |
| Timeout | time.Duration | API 请求的超时时间 | 否 (默认: 30s) |

## 使用示例

### 基本用法

```go
package main

import (
    "context"
    "fmt"
    "log"
    "time"

    "github.com/cloudwego/eino/components/retriever"
    "code.devops.xiaohongshu.com/cloud-native/talos/pkg/components/retriever/allin"
)

func main() {
    // 创建上下文
    ctx := context.Background()

    // 配置 Allin retriever
    config := &allin.RetrieverConfig{
        Endpoint: "https://aiplat-gateway.devops.beta.xiaohongshu.com",
        APPKey:   "your-app-key",
        APPID:    "knowledge4talos",
        Timeout:  30 * time.Second,
    }

    // 创建 retriever 实例
    retriever, err := allin.NewRetriever(ctx, config)
    if err != nil {
        log.Fatalf("Failed to create retriever: %v", err)
    }

    // 执行检索
    query := "自建集群新建容器网络插件"
    docs, err := retriever.Retrieve(ctx, query)
    if err != nil {
        log.Fatalf("Retrieval failed: %v", err)
    }

    // 处理检索结果
    fmt.Printf("Found %d documents for query: %s\n", len(docs), query)
    for i, doc := range docs {
        fmt.Printf("Document %d:\n", i+1)
        fmt.Printf("  Content: %s\n", doc.Content)
        fmt.Printf("  Role: %s\n", doc.MetaData["role"])
        fmt.Println()
    }
}
```

### 高级用法

```go
// 使用选项覆盖默认配置
docs, err := retriever.Retrieve(
    ctx, 
    "自建集群新建容器网络插件",
    retriever.WithTopK(10),            // 覆盖默认的 TopK 值
    retriever.WithScoreThreshold(0.5), // 覆盖默认的分数阈值
)
```

## API 请求格式

### 请求示例

```bash
curl --request POST \
  --url https://aiplat-gateway.devops.beta.xiaohongshu.com/allin-workflow-knowledge4talos/pipelines/main \
  --header 'content-type: application/json' \
  --header 'APP_ID: knowledge4talos' \
  --header 'APP_KEY: your-app-key' \
  --data '{"query": "自建集群新建容器网络插件"}'
```

### 响应格式

```json
{
  "replies": [
    {
      "role": "system",
      "meta": {},
      "name": null,
      "content": [
        {
          "text": "根据检索到的信息，我将为您介绍自建集群新建容器网络插件的相关内容..."
        }
      ]
    },
    {
      "role": "user",
      "meta": {},
      "name": null,
      "content": [
        {
          "text": "用户的问题是: 自建集群新建容器网络插件"
        }
      ]
    }
  ]
}
```

## 实现细节

Allin Retriever 的实现包括以下主要组件：

1. **RetrieverConfig**: 配置结构体，包含连接和查询参数
2. **Retriever**: 实现 `retriever.Retriever` 接口的主要结构体
3. **Request/Response**: 用于 API 通信的请求和响应结构体
4. **Reply/Content**: 表示 API 响应中的回复和内容结构
5. **辅助方法**: 包括请求构建、HTTP 调用和响应转换等功能

### 请求处理流程

1. 构建请求 (`buildRequest`): 创建包含查询的请求对象
2. 发送请求 (`doRequest`): 将请求发送到 Allin API 端点
3. 处理响应 (`convertToDocuments`): 将 API 响应转换为 Document 对象
4. 生成文档 ID (`generateDocumentID`): 为每个文档生成唯一标识符

### 文档转换

API 响应中的每个回复都会被转换为一个或多个 Document 对象：

```go
// ToDocuments 将 Reply 转换为 Document 切片
func (r *Reply) ToDocuments() []*schema.Document {
    docs := make([]*schema.Document, 0, len(r.Content))
    
    // 跳过非系统回复
    if r.Role != string(schema.System) {
        return docs
    }
    
    // 处理每个内容
    for i, content := range r.Content {
        doc := &schema.Document{
            ID:       generateDocumentID(content.Text, i),
            Content:  content.Text,
            MetaData: make(map[string]any),
        }
        
        // 添加元数据
        if r.Meta != nil {
            maps.Copy(doc.MetaData, r.Meta)
        }
        
        // 添加角色信息
        doc.MetaData["role"] = r.Role
        
        docs = append(docs, doc)
    }
    
    return docs
}
```

## 错误处理

Retriever 提供了全面的错误处理，包括：

- 配置验证错误
- HTTP 请求错误
- 响应解析错误
- 超时处理

所有错误都会通过 eino 的回调机制进行传播，并返回给调用者。

## 扩展

您可以通过以下方式扩展 Allin Retriever：

1. 添加新的请求参数
2. 自定义响应处理逻辑
3. 实现额外的回调处理
4. 添加更多的元数据字段

## 注意事项

- 确保提供有效的 APP 密钥和端点
- 对于大型知识库，考虑增加超时时间
- 处理检索结果时注意检查文档的角色信息
- 系统会自动为每个文档生成唯一的 ID，基于内容哈希和索引
