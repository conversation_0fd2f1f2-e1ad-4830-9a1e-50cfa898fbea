package allin

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/cloudwego/eino-ext/components/model/deepseek"
	"github.com/cloudwego/eino-ext/components/model/qwen"
	"github.com/cloudwego/eino/components/model"
)

type ModelType string

const (
	// ModelDeepSeek is the model name for DeepSeek.
	ModelDeepSeek ModelType = "DeepSeek"
	// ModelQwen is the model name for Qwen.
	ModelQwen ModelType = "Qwen"
)

// ChatModelConfig holds the configuration for the Allin chat model.
type ChatModelConfig struct {
	// Base URL for the API endpoint
	BaseURL string
	// API key for authentication
	APIKey string
	// Specific model to use
	Model string
	// Whether to enable streaming, default is false
	Stream bool
	// Maximum number of tokens in the response, default is 8192
	MaxTokens int
	// Controls randomness (0.0 to 1.0), default is 0.7
	Temperature float32
}

// BuildBaseChatModel builds a base chat model based on the configuration.
//
// The configuration is used to initialize a chat model of the appropriate type.
// The model type is determined by the prefix of the model name:
//
//   - "deepseek" for DeepSeek models
//   - "qwen" for Qwen models
//
// If the model name does not match any of the above prefixes, an empty string
// is returned, indicating that the model type is unknown.
func (c *ChatModelConfig) BuildBaseChatModel(ctx context.Context) (model.ToolCallingChatModel, error) {
	switch modelType := selectModel(c.Model); modelType {
	case ModelDeepSeek:
		cfg := &deepseek.ChatModelConfig{
			APIKey:      c.APIKey,
			Timeout:     time.Minute * 30,
			BaseURL:     c.BaseURL,
			Model:       c.Model,
			MaxTokens:   c.MaxTokens,
			Temperature: c.Temperature,
		}
		return newDeepSeekChatModel(ctx, cfg)
	case ModelQwen:
		thinking := true
		cfg := &qwen.ChatModelConfig{
			APIKey:         c.APIKey,
			Timeout:        time.Minute * 30,
			BaseURL:        c.BaseURL,
			Model:          c.Model,
			MaxTokens:      &c.MaxTokens,
			Temperature:    &c.Temperature,
			EnableThinking: &thinking,
		}
		return newQwenChatModel(ctx, cfg)
	default:
		return nil, fmt.Errorf("unsupported model: %s", c.Model)
	}
}

// selectModel selects the model type based on the model name.
//
// The model name is matched against the following prefixes:
//
//   - "deepseek" for DeepSeek models
//   - "qwen" for Qwen models
//
// If the model name does not match any of the above prefixes, an empty string
// is returned, indicating that the model type is unknown.
func selectModel(model string) ModelType {
	switch {
	case strings.HasPrefix(model, "deepseek"):
		return ModelDeepSeek
	case strings.HasPrefix(model, "qwen"):
		return ModelQwen
	}
	return ""
}

// newDeepSeekChatModel initializes and returns a DeepSeek chat model.
func newDeepSeekChatModel(ctx context.Context, cfg *deepseek.ChatModelConfig) (*deepseek.ChatModel, error) {
	model, err := deepseek.NewChatModel(ctx, cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize DeepSeek chat model: %w", err)
	}

	return model, nil
}

// newQwenChatModel initializes and returns a Qwen chat model.
func newQwenChatModel(ctx context.Context, cfg *qwen.ChatModelConfig) (*qwen.ChatModel, error) {
	model, err := qwen.NewChatModel(ctx, cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize Qwen chat model: %w", err)
	}

	return model, nil
}
