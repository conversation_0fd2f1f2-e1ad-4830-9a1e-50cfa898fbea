package auth

import (
	"context"
	"fmt"
	"net/http"
)

func init() {
	// Initialize the default provider
	defaultProvider = NewProvider()
}

// defaultProvider is the default authentication provider
var defaultProvider AuthProvider

// AuthProvider defines the interface for authentication
type AuthProvider interface {
	// Authenticate authenticates the user with the provided credentials
	Authenticate(ctx context.Context, credentials any) (UserInfo, error)
	// ValidateToken validates the token and retrieves user information
	ValidateToken(token string) (UserInfo, error)
}

// Ensure that the provider implements the AuthProvider interface
var _ AuthProvider = (*provider)(nil)

// provider implements the AuthProvider interface
type provider struct{}

// NewProvider creates a new provider instance
func NewProvider() AuthProvider {
	return &provider{}
}

func (p *provider) Authenticate(ctx context.Context, credentials any) (UserInfo, error) {
	// TODO: Use JWT to authenticate
	return UserInfo{}, nil
}

func (p *provider) ValidateToken(token string) (UserInfo, error) {
	// TODO: Use JWT to validate the token
	return UserInfo{}, nil
}

// UserInfo contains user information
type UserInfo struct {
	// Username is the unique identifier for the user
	Username string
	// Roles is a list of roles assigned to the user
	Roles []string
	// Teams is a list of teams the user belongs to
	Teams []string
}

// AuthFromRequest extracts the authentication key from the request header.
// It uses the authProvider to validate the token and retrieve user information
func AuthFromRequest(ctx context.Context, r *http.Request) context.Context {
	token := r.Header.Get("Authorization")
	userInfo, err := defaultProvider.ValidateToken(token)
	if err != nil {
		return ctx
	}

	return WithAuthKey(ctx, userInfo)
}

// authKey is a custom context key for authentication
type authKey struct{}

// WithAuthKey adds an authentication key to the context
func WithAuthKey(ctx context.Context, value UserInfo) context.Context {
	return context.WithValue(ctx, authKey{}, value)
}

// AuthFromEnv extracts the authentication key from the environment variable
func AuthFromEnv(ctx context.Context) context.Context {
	return WithAuthKey(ctx, UserInfo{})
}

// TokenFromContext retrieves the authentication token from the context
func TokenFromContext(ctx context.Context) (UserInfo, error) {
	auth, ok := ctx.Value(authKey{}).(UserInfo)
	if !ok {
		return UserInfo{}, fmt.Errorf("failed to get auth key from context")
	}

	return auth, nil
}
