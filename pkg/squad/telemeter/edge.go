package telemeter

import (
	"context"

	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/config"
	talosmcp "code.devops.xiaohongshu.com/cloud-native/talos/pkg/mcp"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/version"
	"github.com/mark3labs/mcp-go/mcp"
)

// Edge defines the interface for the data source layer
type Edge interface {
	// GetSources returns a slice of all enabled data sources
	GetSources() []Source
}

// Ensure edge implements Edge interface
var _ Edge = (*edge)(nil)

// edge implements the data source layer for collecting various data types
type edge struct {
	// Map of data sources, where the key is the data type and the value is the source itself
	dataSources map[DataType]Source
}

// newEdge creates a new Edge instance with configured data sources
func newEdge(ctx context.Context, config *config.SourceConfig) (Edge, error) {
	// Initialize a map to store different types of data sources
	dataSources := make(map[DataType]Source)

	// Configure Events source if enabled
	if config.EnableEvents {
		// Set up client info for events source
		clientInfo := mcp.Implementation{
			Name:    "talos-events-source",
			Version: version.GitCommit,
		}
		// Create a new MCP client for events
		cli, err := talosmcp.NewMCPClient(ctx, config.MCPs["events"], clientInfo)
		if err != nil {
			return nil, err
		}
		// Add new event source to dataSources map
		dataSources[Events] = newEventSource(ctx, cli, config)
	}

	// Configure Logs source if enabled
	if config.EnableLogs {
		// Set up client info for logs source
		clientInfo := mcp.Implementation{
			Name:    "talos-logs-source",
			Version: version.GitCommit,
		}
		// Create a new MCP client for logs
		cli, err := talosmcp.NewMCPClient(ctx, config.MCPs["logs"], clientInfo)
		if err != nil {
			return nil, err
		}
		// Add new log source to dataSources map
		dataSources[Logs] = newLogSource(ctx, cli, config)
	}

	// Configure Metrics source if enabled
	if config.EnableMetrics {
		// Set up client info for metrics source
		clientInfo := mcp.Implementation{
			Name:    "talos-metrics-source",
			Version: version.GitCommit,
		}
		// Create a new MCP client for metrics
		cli, err := talosmcp.NewMCPClient(ctx, config.MCPs["metrics"], clientInfo)
		if err != nil {
			return nil, err
		}
		// Add new metric source to dataSources map
		dataSources[Metrics] = newMetricSource(ctx, cli, config)
	}

	// Configure Traces source if enabled
	if config.EnableTraces {
		// Set up client info for traces source
		clientInfo := mcp.Implementation{
			Name:    "talos-traces-source",
			Version: version.GitCommit,
		}
		// Create a new MCP client for traces
		cli, err := talosmcp.NewMCPClient(ctx, config.MCPs["traces"], clientInfo)
		if err != nil {
			return nil, err
		}
		// Add new trace source to dataSources map
		dataSources[Traces] = newTraceSource(ctx, cli, config)
	}

	// Return a new edge instance with configured data sources
	return &edge{dataSources: dataSources}, nil
}

// GetSources returns a slice of all enabled data sources
func (e *edge) GetSources() []Source {
	// Initialize a slice to store enabled sources
	sources := make([]Source, 0, len(e.dataSources))
	// Iterate through all data sources
	for _, source := range e.dataSources {
		// Check if the source is enabled
		if source.IsEnabled() {
			// Add enabled source to the slice
			sources = append(sources, source)
		}
	}
	// Return the slice of enabled sources
	return sources
}
