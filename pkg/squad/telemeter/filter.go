package telemeter

import (
	"slices"
	"strings"
	"time"

	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/config"
	corev1 "k8s.io/api/core/v1"
)

// Filter defines the interface for filtering data
type Filter interface {
	// Filter filters the provided data based on configured rules
	Filter(events []*corev1.Event) []*corev1.Event
}

// Ensure filter implements Filter interface
var _ Filter = (*filter)(nil)

// filter implements the data filtering logic
type filter struct {
	// Configuration for the filter
	config *config.FilterConfig
	// Matcher for pattern recognition
	patterns Matcher
	// Statistics component for data analysis
	statistics Statistics
}

// newFilter creates a new data filter instance
func newFilter(config *config.FilterConfig) Filter {
	patterns := CommonPatterns()
	matcher := newMatcher()

	for _, p := range patterns {
		if p.Category == "filter" || p.Category == "emergency" {
			matcher.AddPattern(p.Name, p.Description, p.Match)
		}
	}

	return &filter{
		config:     config,
		patterns:   matcher,
		statistics: newStatistics(),
	}
}

// Filter filters data based on configured rules
func (f *filter) Filter(events []*corev1.Event) []*corev1.Event {
	// Initialize a slice to store filtered data
	var filtered []*corev1.Event
	for _, event := range events {
		// Check if the event should be included based on filtering rules
		if f.shouldIncludeEvent(event) {
			filtered = append(filtered, event)
		}
	}
	return filtered
}

// shouldIncludeEvent determines if the data should be included based on filters.
// See example: testdata/events.json
func (f *filter) shouldIncludeEvent(event *corev1.Event) bool {
	// Check event type
	if !f.matchEventType(event) {
		return false
	}

	// Check network filters
	if !f.matchNetworkFilters(event) {
		return false
	}

	// Check rate limiting
	if f.isRateLimited(event) {
		return false
	}

	// If all checks pass, the event is included
	return true
}

// matchEventType checks if the data is a kubernetes event and its type matches configured types
func (f *filter) matchEventType(event *corev1.Event) bool {
	// Combine default event types with configured event types
	allowedEventTypes := append([]string{"Warning", "Normal"}, f.config.EventTypes...)
	// Use slices.Contains to check if the event type is in the allowed types
	return slices.Contains(allowedEventTypes, event.Type)
}

// Default Network Filter
var DefaultNetworkFilters = []string{
	// TODO: add more network events
}

// matchNetworkFilters checks if the data is a kubernetes event and its reason matches network-related filters
func (f *filter) matchNetworkFilters(event *corev1.Event) bool {
	// Combine default network filters with configured network filters
	networkFilters := append(DefaultNetworkFilters, f.config.NetworkFilters...)
	// Iterate through all network filters
	for _, filter := range networkFilters {
		// If the reason contains the filter string, return true
		if strings.Contains(event.Reason, filter) {
			return true
		}
	}
	// If no match is found, return false
	return false
}

// isRateLimited checks if the event should be rate limited based on its frequency
func (f *filter) isRateLimited(event *corev1.Event) bool {
	// Generate a unique key for the event
	key := f.getEventKey(event)
	// Retrieve or create statistics for the event
	stat, exists := f.statistics.Get(key)
	if !exists {
		// If it's a new event, update statistics and allow it through
		f.statistics.Update(key)
		return false
	}

	// Calculate time since last occurrence
	timeSinceLastSeen := time.Since(stat.lastSeen)
	// Update statistics for this event
	f.statistics.Update(key)

	// Implement adaptive rate limiting based on event frequency
	if stat.count < 10 {
		// For infrequent events, allow if more than 1 second has passed
		return timeSinceLastSeen < time.Second
	} else if stat.count < 100 {
		// For moderately frequent events, increase the limit to 5 seconds
		return timeSinceLastSeen < 5*time.Second
	} else {
		// For very frequent events, increase the limit to 10 seconds
		return timeSinceLastSeen < 10*time.Second
	}
}

// getEventKey generates a unique key for an event
func (f *filter) getEventKey(event *corev1.Event) string {
	namespace := event.Namespace
	name := event.Name
	reason := event.Reason
	return namespace + "/" + name + "/" + reason
}
