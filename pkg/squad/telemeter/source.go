package telemeter

import (
	"context"
	"fmt"

	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/config"
	talosmcp "code.devops.xiaohongshu.com/cloud-native/talos/pkg/mcp"
	"github.com/mark3labs/mcp-go/client"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
)

// DataType represents different types of data sources
type DataType string

// Constants representing different types of data sources
const (
	// Logs represents system logs data source
	Logs DataType = "logs"
	// Metrics represents system metrics data source
	Metrics DataType = "metrics"
	// Traces represents distributed tracing data source
	Traces DataType = "traces"
	// Events represents Kubernetes events data source
	Events DataType = "events"
)

// Source defines the interface for all data sources
type Source interface {
	// Collect retrieves data from the source
	Collect(ctx context.Context, args map[string]any) ([]unstructured.Unstructured, error)
	// IsEnabled returns whether this data source is enabled
	IsEnabled() bool
}

// Ensure source implements Source interface
var _ Source = (*source)(nil)

// source provides base implementation for MCP-based data sources
type source struct {
	mcpClient *client.Client
	enabled   bool
}

// Collect retrieves data from the MCP source
func (s *source) Collect(ctx context.Context, args map[string]any) ([]unstructured.Unstructured, error) {
	// Early return if the source is disabled
	if !s.enabled {
		return nil, nil
	}

	// Call the MCP tool to list resources and handle errors
	data, err := talosmcp.CallMCPTool(ctx, s.mcpClient, "list_resources", args)
	if err != nil {
		// If there's an error calling the MCP tool, wrap it and return
		return nil, fmt.Errorf("failed to call MCP tool: %w", err)
	}

	var result unstructured.UnstructuredList
	if err := result.UnmarshalJSON([]byte(data)); err != nil {
		return nil, err
	}

	return result.Items, nil
}

// IsEnabled returns whether this MCP source is enabled
func (s *source) IsEnabled() bool {
	return s.enabled
}

// eventSource implements DataSource for Kubernetes events
type eventSource struct {
	source
}

// newEventSource creates a new Kubernetes event source
func newEventSource(ctx context.Context, mcpClient *client.Client, config *config.SourceConfig) Source {
	return &eventSource{
		source: source{
			mcpClient: mcpClient,
			enabled:   config.EnableEvents,
		},
	}
}

// logSource implements DataSource for logs
type logSource struct {
	source
}

// newLogSource creates a new system log source
func newLogSource(ctx context.Context, mcpClient *client.Client, config *config.SourceConfig) Source {
	return &logSource{
		source: source{
			mcpClient: mcpClient,
			enabled:   config.EnableLogs,
		},
	}
}

// metricSource implements DataSource for metrics
type metricSource struct {
	source
}

// newMetricSource creates a new system metric source
func newMetricSource(ctx context.Context, mcpClient *client.Client, config *config.SourceConfig) Source {
	return &metricSource{
		source: source{
			mcpClient: mcpClient,
			enabled:   config.EnableMetrics,
		},
	}
}

// traceSource implements DataSource for distributed tracing
type traceSource struct {
	source
}

// newTraceSource creates a new distributed trace source
func newTraceSource(ctx context.Context, mcpClient *client.Client, config *config.SourceConfig) Source {
	return &traceSource{
		source: source{
			mcpClient: mcpClient,
			enabled:   config.EnableTraces,
		},
	}
}
