package telemeter

import (
	"sync"
	"time"
)

// Statistics defines the interface for tracking source statistics
type Statistics interface {
	// Update updates the statistics for a given source
	Update(key string)
	// Get returns the statistics for a given source
	Get(key string) (*sourceStat, bool)
}

// Ensure sourceStatistics implements Statistics interface
var _ Statistics = (*sourceStatistics)(nil)

// sourceStatistics tracks source statistics
type sourceStatistics struct {
	// Mutex for safe concurrent access to the stats map
	sync.RWMutex
	// Map to store statistics for each source
	stats map[string]*sourceStat
}

// sourceStat represents statistics for a source
type sourceStat struct {
	// count represents the number of times this source has been seen
	count int
	// firstSeen is the timestamp when this source was first observed
	firstSeen time.Time
	// lastSeen is the timestamp of the most recent observation of this source
	lastSeen time.Time
}

// newStatistics creates and initializes a new source statistics instance
// It returns an interface of type Statistics for better abstraction
func newStatistics() Statistics {
	return &sourceStatistics{
		stats: make(map[string]*sourceStat),
	}
}

// Update updates the statistics for a given source.
func (s *sourceStatistics) Update(key string) {
	s.Lock()
	defer s.Unlock()

	// Check if the key already exists in the stats map
	if _, ok := s.stats[key]; !ok {
		// If the key doesn't exist, create a new sourceStat entry
		s.stats[key] = &sourceStat{
			count:     1,          // Initialize count to 1
			firstSeen: time.Now(), // Set firstSeen to current time
			lastSeen:  time.Now(), // Set lastSeen to current time
		}
	} else {
		// If the key exists, increment the count
		s.stats[key].count++
		// Update the lastSeen timestamp to current time
		s.stats[key].lastSeen = time.Now()
	}
}

// Get returns the statistics for a given source.
func (s *sourceStatistics) Get(key string) (*sourceStat, bool) {
	s.RLock()
	defer s.RUnlock()

	// Retrieve the statistics for the given key
	stat, exists := s.stats[key]
	// Return the statistics and a boolean indicating whether the key exists
	return stat, exists
}
