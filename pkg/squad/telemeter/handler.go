package telemeter

import (
	"math"
	"slices"
	"sort"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
)

// Handler defines the interface for event handling and analysis
type Handler interface {
	// <PERSON><PERSON> processes a batch of events and returns analysis results
	// events: A slice of corev1.Event representing individual events
	// Returns: A map containing analysis results for the processed events
	Handle(events []*corev1.Event) map[string]any
}

// Ensure handler implements Handler interface
var _ Handler = (*handler)(nil)

// handler implements event analysis and correlation
type handler struct {
	// Pattern matcher for event analysis
	matcher Matcher
}

// new<PERSON><PERSON><PERSON> creates a new event analyzer instance
func newHandler() Handler {
	// Get common patterns for analysis
	patterns := CommonPatterns()
	// Create a new pattern matcher
	matcher := newMatcher()

	// Add analysis-specific patterns to the matcher
	for _, p := range patterns {
		if p.Category == "analysis" {
			matcher.AddPattern(p.Name, p.Description, p.Match)
		}
	}
	// Return a new analyzer with initialized pattern matcher
	return &handler{
		matcher: newMatcher(),
	}
}

// <PERSON><PERSON> analyzes a batch of events
func (a *handler) Handle(events []*corev1.Event) map[string]any {
	results := make(map[string]any)

	// Group events by component
	componentEvents := a.groupByComponent(events)

	// Analyze patterns for each component
	for component, events := range componentEvents {
		patterns := a.detectPatterns(events)
		if len(patterns) > 0 {
			results[component] = patterns
		}
	}

	// Analyze temporal relationships
	temporalPatterns := a.analyzeTemporalPatterns(events)
	if len(temporalPatterns) > 0 {
		results["temporal_patterns"] = temporalPatterns
	}

	// Analyze impact severity
	impacts := a.analyzeImpact(events)
	if len(impacts) > 0 {
		results["impacts"] = impacts
	}

	return results
}

// groupByComponent efficiently groups events by their source component
func (a *handler) groupByComponent(events []*corev1.Event) map[string][]*corev1.Event {
	grouped := make(map[string][]*corev1.Event)
	for _, event := range events {
		// Extract component name safely using type assertions
		componentName := "unknown"
		if event.Source.Component != "" {
			componentName = event.Source.Component
		}
		// Append event to the appropriate component group
		grouped[componentName] = append(grouped[componentName], event)
	}
	return grouped
}

// detectPatterns detects patterns in a set of events
func (a *handler) detectPatterns(events []*corev1.Event) []string {
	// Use a map to store unique patterns for efficient lookup
	patternMap := make(map[string]struct{})

	// Iterate through all patterns and events only once
	for _, event := range events {
		for _, pattern := range a.matcher.GetPatterns() {
			// Check if the pattern matches and hasn't been added yet
			if pattern.Match(event) {
				patternMap[pattern.Name] = struct{}{}
			}
		}
	}

	// Convert the map keys to a slice for the return value
	patterns := make([]string, 0, len(patternMap))
	for patternName := range patternMap {
		patterns = append(patterns, patternName)
	}

	return patterns
}

// analyzeTemporalPatterns analyzes temporal relationships between events
func (a *handler) analyzeTemporalPatterns(events []*corev1.Event) []map[string]any {
	var patterns []map[string]any

	// Sort events by timestamp in chronological order
	sortedEvents := a.sortEventsByTime(events)

	// Look for events that occurred close together
	for i := range sortedEvents[:len(sortedEvents)-1] {
		current := sortedEvents[i]
		next := sortedEvents[i+1]

		if a.areEventsRelated(current, next) {
			pattern := map[string]any{
				"type":        "temporal_correlation",
				"first_event": current,
				"next_event":  next,
				"confidence":  a.calculateCorrelationConfidence(current, next),
			}
			patterns = append(patterns, pattern)
		}
	}

	return patterns
}

// sortEventsByTime sorts events by timestamp in chronological order
func (a *handler) sortEventsByTime(events []*corev1.Event) []*corev1.Event {
	// If there's 1 or fewer events, no sorting is needed
	if len(events) <= 1 {
		return events
	}

	// Use sort.Slice to sort the events slice
	sort.Slice(events, func(i, j int) bool {
		// Safely extract timestamp strings from events
		ts1 := events[i].LastTimestamp
		ts2 := events[j].LastTimestamp
		// Compare the parsed times
		return ts1.Before(&ts2)
	})

	// Return the sorted events slice
	return events
}

// areEventsRelated determines if two events are related
func (a *handler) areEventsRelated(event1, event2 *corev1.Event) bool {
	// Check if events are from the same namespace and component
	if !haveSameSource(event1, event2) {
		return false
	}

	// Choose appropriate time window based on event types
	maxDuration := a.getTimeWindowForEventType(event1, event2)

	// Check temporal proximity
	if !areTemporallyClose(event1, event2, maxDuration) {
		return false
	}

	// Check if events have related resources
	if haveRelatedResources(event1, event2) {
		return true
	}

	// Check if they are part of a known pattern
	return arePartOfPattern(event1, event2)
}

// haveSameSource checks if two events originate from the same source
func haveSameSource(event1, event2 *corev1.Event) bool {
	// Compare namespaces
	if event1.Namespace != event2.Namespace {
		return false
	}

	// Compare source components
	return event1.Source.Component == event2.Source.Component
}

// getTimeWindowForEventType returns appropriate time window based on event types
func (a *handler) getTimeWindowForEventType(event1, event2 *corev1.Event) time.Duration {
	// Get event types
	type1 := event1.Type
	type2 := event2.Type

	// Use a shorter time window for urgent events
	if type1 == "Warning" || type2 == "Warning" {
		return 2 * time.Minute
	}

	// Default to 5 minutes for other event types
	return 5 * time.Minute
}

// areTemporallyClose checks if two events occurred within a specified duration
func areTemporallyClose(event1, event2 *corev1.Event, maxDuration time.Duration) bool {
	// Extract timestamps
	ts1 := event1.LastTimestamp
	ts2 := event2.LastTimestamp

	// Check if timestamps are valid
	if ts1.IsZero() || ts2.IsZero() {
		return false
	}

	// Calculate the duration between events
	duration := ts2.Sub(ts1.Time)
	if duration < 0 {
		duration = -duration
	}

	// Check if events occurred within maxDuration of each other
	return duration <= maxDuration
}

// haveRelatedResources checks if two events reference the same resource
func haveRelatedResources(event1, event2 *corev1.Event) bool {
	// Check if events reference the same resource
	name1 := event1.InvolvedObject.Name
	name2 := event2.InvolvedObject.Name
	if name1 == "" || name2 == "" {
		return false
	}

	// Check if resources are the same or have owner/dependent relationship
	return name1 == name2
}

// arePartOfPattern checks if two events are part of a known pattern
func arePartOfPattern(event1, event2 *corev1.Event) bool {
	// Extract reasons from events
	if event1.Reason == "" || event2.Reason == "" {
		return false
	}

	// Define known related event patterns
	patterns := map[string][]string{
		"NodeNotReady":      {"NodeReady", "NodeStatusUnknown"},
		"ContainerCreating": {"Started", "Created"},
		"FailedMount":       {"MountVolume.SetUp", "AttachVolume.Attach"},
		// TODO: Add more patterns as needed
	}

	return matchesPattern(event1.Reason, event2.Reason, patterns)
}

// matchesPattern checks if two reasons match a known pattern
func matchesPattern(reason1, reason2 string, patterns map[string][]string) bool {
	for trigger, related := range patterns {
		if (reason1 == trigger && slices.Contains(related, reason2)) ||
			(reason2 == trigger && slices.Contains(related, reason1)) {
			return true
		}
	}
	return false
}

// calculateCorrelationConfidence calculates the confidence score for event correlation
func (a *handler) calculateCorrelationConfidence(event1, event2 *corev1.Event) float64 {
	var score float64 = 0.0

	// Calculate time proximity score (max 0.3)
	// This measures how close the events are in time
	timeScore := a.calculateTimeProximityScore(event1, event2)
	score += timeScore * 0.3

	// Calculate resource relation score (max 0.3)
	// This evaluates how closely the resources involved in the events are related
	resourceScore := a.calculateResourceScore(event1, event2)
	score += resourceScore * 0.3

	// Calculate event pattern match score (max 0.4)
	// This checks how well the events match known patterns
	patternScore := a.calculatePatternScore(event1, event2)
	score += patternScore * 0.4

	// Ensure the final score doesn't exceed 1.0
	return math.Min(1.0, score)
}

// calculateTimeProximityScore calculates how close events are in time
func (a *handler) calculateTimeProximityScore(event1, event2 *corev1.Event) float64 {
	ts1 := event1.LastTimestamp
	ts2 := event2.LastTimestamp
	if ts1.IsZero() || ts2.IsZero() {
		return 0.0
	}

	time1 := ts1.Time
	time2 := ts2.Time

	// Calculate the difference in time (in minutes)
	duration := time2.Sub(time1)
	if duration < 0 {
		duration = -duration
	}
	minutes := duration.Minutes()

	// The smaller the time difference, the higher the score
	switch {
	case minutes < 1:
		return 1.0
	case minutes < 2:
		return 0.8
	case minutes < 5:
		return 0.6
	case minutes < 10:
		return 0.3
	default:
		return 0.0
	}
}

// calculateResourceScore evaluates how closely resources are related
func (a *handler) calculateResourceScore(event1, event2 *corev1.Event) float64 {
	// Check if the resources are exactly the same
	if isSameResource(event1, event2) {
		return 1.0
	}

	// Check if there's an owner/dependent relationship between resources
	if hasOwnerRelationship(event1, event2) {
		return 0.8
	}

	// Check if the resources are in the same namespace
	if isSameNamespace(event1, event2) {
		return 0.4
	}

	// Check if the resources are from the same component
	if isSameComponent(event1, event2) {
		return 0.2
	}

	// If none of the above conditions are met, return 0.0
	return 0.0
}

// calculatePatternScore evaluates how well events match known patterns
func (a *handler) calculatePatternScore(event1, event2 *corev1.Event) float64 {
	if event1.Reason == "" || event2.Reason == "" {
		return 0.0
	}

	// Known strong patterns
	strongPatterns := map[string][]string{
		"NodeNotReady":      {"NodeReady"},
		"ContainerCreating": {"Started"},
		"FailedMount":       {"MountVolume.SetUp"},
	}

	// Known weak patterns
	weakPatterns := map[string][]string{
		"NodeNotReady":      {"NodeStatusUnknown"},
		"ContainerCreating": {"Pulling"},
		"FailedMount":       {"VolumeResizeFailed"},
	}

	// Check strong patterns
	if matchesPattern(event1.Reason, event2.Reason, strongPatterns) {
		return 1.0
	}

	// Check weak patterns
	if matchesPattern(event1.Reason, event2.Reason, weakPatterns) {
		return 0.6
	}

	// Check event types
	if event1.Type == event2.Type {
		return 0.3
	}

	return 0.0
}

// isSameResource checks if two events involve the same resource
func isSameResource(event1, event2 *corev1.Event) bool {
	return event1.Name == event2.Name
}

// hasOwnerRelationship checks if there's an owner/dependent relationship between resources
func hasOwnerRelationship(event1, event2 *corev1.Event) bool {
	name1 := event1.InvolvedObject.Name
	name2 := event2.InvolvedObject.Name
	namespace1 := event1.InvolvedObject.Namespace
	namespace2 := event2.InvolvedObject.Namespace

	return (name1 == name2 && namespace1 == namespace2)
}

// isSameNamespace checks if two events are in the same namespace
func isSameNamespace(event1, event2 *corev1.Event) bool {
	return event1.Namespace == event2.Namespace
}

// isSameComponent checks if two events are from the same component
func isSameComponent(event1, event2 *corev1.Event) bool {
	return event1.Source.Component == event2.Source.Component
}

// analyzeImpact analyzes the impact severity of events
func (a *handler) analyzeImpact(events []*corev1.Event) []map[string]any {
	var impacts []map[string]any

	for _, event := range events {
		impact := a.calculateEventImpact(event)
		if impact["severity"].(string) != "low" {
			impacts = append(impacts, impact)
		}
	}

	return impacts
}

// calculateEventImpact calculates the impact of an event
func (a *handler) calculateEventImpact(event *corev1.Event) map[string]any {
	impact := make(map[string]any)

	// Basic impact calculation
	impact["severity"] = a.calculateSeverity(event)
	impact["scope"] = a.calculateScope(event)
	impact["duration"] = a.calculateDuration(event)

	return impact
}

// calculateSeverity determines the severity level of an event
func (a *handler) calculateSeverity(event *corev1.Event) string {
	eventType := event.Type
	reason := event.Reason
	kind := event.InvolvedObject.Kind
	namespace := event.Namespace

	criticalReasons := map[string]bool{
		"Failed": true, "Error": true, "NodeNotReady": true, "OutOfMemory": true,
		"CrashLoopBackOff": true, "ImagePullBackOff": true, "FailedMount": true,
	}
	criticalResources := map[string]bool{"Node": true, "PersistentVolume": true, "Namespace": true}
	systemNamespaces := map[string]bool{"kube-system": true, "monitoring": true, "ingress-nginx": true}

	switch {
	case eventType == corev1.EventTypeWarning:
		return "high"
	case criticalReasons[reason]:
		return "high"
	case criticalResources[kind]:
		return "medium"
	case systemNamespaces[namespace]:
		return "medium"
	default:
		return "low"
	}
}

// calculateScope determines the impact scope of an event
func (a *handler) calculateScope(event *corev1.Event) string {
	// Define cluster-level resources
	clusterResources := []string{"Node", "ClusterRole", "PersistentVolume", "Namespace", "StorageClass", "ClusterIssuer"}
	// Check resource type
	if slices.Contains(clusterResources, event.InvolvedObject.Kind) {
		return "cluster"
	}

	// Check namespace
	if event.Namespace == "kube-system" {
		return "cluster"
	}

	// Check if multiple namespaces are affected
	if affectsMultipleNamespaces(event) {
		return "namespace"
	}
	return "local"
}

// calculateDuration estimates the duration impact of an event
func (a *handler) calculateDuration(event *corev1.Event) string {
	// Check if the event is already resolved
	if isResolved(event) {
		return "short"
	}

	// Calculate event duration
	startTime := event.FirstTimestamp.Time
	lastTime := event.LastTimestamp.Time
	// If both timestamps are available, calculate the duration
	if !startTime.IsZero() && !lastTime.IsZero() {
		duration := lastTime.Sub(startTime)
		switch {
		case duration > 24*time.Hour:
			return "long"
		case duration > 1*time.Hour:
			return "medium"
		case duration > 5*time.Minute:
			return "short"
		default:
			return "transient"
		}
	}

	// Check for persistent issues
	persistentIssues := []string{
		"CrashLoopBackOff",
		"ImagePullBackOff",
		"NodeNotReady",
		"PersistentVolumeError",
		//TODO: Add more persistent issues here
	}
	// If the reason is in the list of persistent issues, consider it long-lasting
	if slices.Contains(persistentIssues, event.Reason) {
		return "long"
	}
	// Default to short duration if no other conditions are met
	return "short"
}

// isResolved checks if an event has been resolved based on its reason
func isResolved(event *corev1.Event) bool {
	// Check if the event reason indicates a resolved state
	resolvedReasons := map[string]bool{
		"NodeReady":       true,
		"Started":         true,
		"SuccessfulMount": true,
		// TODO:Add more resolved reasons as needed
	}
	return resolvedReasons[event.Reason]
}

// affectsMultipleNamespaces checks if an event affects multiple namespaces
func affectsMultipleNamespaces(event *corev1.Event) bool {
	// Check if the resource kind is cluster-scoped
	kind := event.InvolvedObject.Kind
	clusterScopedResources := map[string]bool{
		"ClusterRole":        true,
		"ClusterRoleBinding": true,
		"Node":               true,
		"PersistentVolume":   true,
		"StorageClass":       true,
		"NetworkPolicy":      true,
	}
	if clusterScopedResources[kind] {
		return true
	}

	// Check if the event message mentions multiple namespaces
	message := event.Message
	if strings.Contains(strings.ToLower(message), "across namespaces") ||
		strings.Contains(strings.ToLower(message), "multiple namespaces") {
		return true
	}

	// Check if the event references resources in different namespaces
	primaryNS := event.Namespace
	if event.InvolvedObject.Namespace != "" && event.InvolvedObject.Namespace != primaryNS {
		return true
	}

	// Check for specific event reasons that typically affect multiple namespaces
	multiNamespaceReasons := map[string]bool{
		"NetworkPolicyChanged":    true,
		"IngressControllerUpdate": true,
		"StorageProvisionFailed":  true,
		"NodeNotReady":            true,
	}
	return multiNamespaceReasons[event.Reason]
}
