// Package telemeter provides functionality for caching and managing telemetry data sources.

package telemeter

import (
	"sync"
	"time"
)

// Cache defines the interface for caching sources
type Cache interface {
	// Update adds or updates a source in the cache
	Update(dataID string, data map[string]any)
	// Get retrieves a source from the cache
	Get(dataID string) (map[string]any, bool)
	// Cleanup removes expired entries from the cache
	Cleanup()
}

// Ensure sourceCache implements Cache interface
var _ Cache = (*sourceCache)(nil)

// sourceCache implements local caching of sources with TTL
type sourceCache struct {
	// Mutex for concurrent access to the cache
	sync.RWMutex
	// Map of cached sources
	sources map[string]*cachedData
	// Time-to-live for cache entries
	ttl time.Duration
}

// cachedData represents a cached source
type cachedData struct {
	// Actual source data
	data map[string]any
	// Time when the source was last updated
	timestamp time.Time
}

// newSourceCache creates a new source cache instance
func newSourceCache(ttl time.Duration) *sourceCache {
	return &sourceCache{
		sources: make(map[string]*cachedData),
		ttl:     ttl,
	}
}

// Get retrieves a source from the cache.
func (s *sourceCache) Get(dataID string) (map[string]any, bool) {
	s.RLock()
	defer s.RUnlock()

	// Attempt to retrieve the cached source data
	cachedData, exists := s.sources[dataID]

	// Return the data and a boolean indicating whether it exists in the cache
	return cachedData.data, exists
}

// Update adds or updates a source in the cache.
func (s *sourceCache) Update(dataID string, data map[string]any) {
	s.Lock()
	defer s.Unlock()

	// Create a new cachedData entry with the provided data and current timestamp
	s.sources[dataID] = &cachedData{
		data:      data,
		timestamp: time.Now(),
	}
}

// Cleanup removes expired entries from the cache.
func (s *sourceCache) Cleanup() {
	s.Lock()
	defer s.Unlock()

	// Iterate through all cached data entries
	for dataID, cachedData := range s.sources {
		// Check if the entry has expired based on its timestamp and TTL
		if time.Since(cachedData.timestamp) > s.ttl {
			// Remove the expired entry from the cache
			delete(s.sources, dataID)
		}
	}
}
