package telemeter

import (
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
)

// Matcher defines the interface for event pattern matching
type Matcher interface {
	// GetPatterns returns a map of all registered patterns
	GetPatterns() map[string]sourcePattern
	// AddPattern registers a new pattern with the given name, description, and matcher function
	AddPattern(name, description string, matcher func(source *corev1.Event) bool)
	// MatchPatterns matches the given event against all registered patterns
	MatchPatterns(event *corev1.Event) []MatchResult
	// DeletePattern removes a pattern by its name
	Delete<PERSON>attern(name string)
	// GetPatternsByCategory returns patterns filtered by category
	GetPatternsByCategory(category string) []sourcePattern
}

// Ensure matcher implements Matcher interface
var _ Matcher = (*matcher)(nil)

// matcher implements event pattern recognition
type matcher struct {
	// Map of patterns, where the key is the pattern name and the value is the pattern itself
	patterns map[string]sourcePattern
}

// sourcePattern represents a pattern for matching events
type sourcePattern struct {
	// Name is the unique identifier for the pattern
	Name string
	// Description provides a brief explanation of what the pattern detects
	Description string
	// Match is a function that evaluates whether a given source matches this pattern
	Match func(data *corev1.Event) bool
	// Severity represents the severity of the event
	Severity string
	// Category is the category of the event
	Category string
	// Components is a list of components that are involved in the event
	Components []string
}

// MatchResult represents the result of matching an event against a pattern
type MatchResult struct {
	// Pattern is the sourcePattern that matched the event
	Pattern sourcePattern
	// Confidence represents the certainty of the match (0.0 to 1.0)
	Confidence float64
	// Context contains additional information about the matched event
	Context map[string]any
	// Timestamp indicates when the match occurred
	Timestamp time.Time
}

// newMatcher creates a new pattern matcher instance
func newMatcher() Matcher {
	return &matcher{
		patterns: make(map[string]sourcePattern),
	}
}

// GetPatterns returns a map of all registered patterns
// This method allows access to the internal patterns for inspection or iteration
func (pm *matcher) GetPatterns() map[string]sourcePattern {
	return pm.patterns
}

// AddPattern registers a new pattern with the given name, description, and matcher function
func (pm *matcher) AddPattern(name, description string, matcher func(source *corev1.Event) bool) {
	pm.patterns[name] = sourcePattern{
		Name:        name,
		Description: description,
		Match:       matcher,
	}
}

// MatchPatterns matches the given event against all registered patterns
func (pm *matcher) MatchPatterns(event *corev1.Event) []MatchResult {
	var results []MatchResult
	for _, pattern := range pm.patterns {
		if pattern.Match(event) {
			results = append(results, MatchResult{
				Pattern:    pattern,
				Confidence: 1.0, // Assuming a perfect match for now
				Context:    generateContext(event),
				Timestamp:  time.Now(),
			})
		}
	}
	return results
}

// generateContext creates a context map from the event
func generateContext(event *corev1.Event) map[string]any {
	return map[string]any{
		"reason":    event.Reason,
		"message":   event.Message,
		"source":    event.Source.Component,
		"namespace": event.Namespace,
		"name":      event.Name,
		"component": event.Source.Component,
		"host":      event.Source.Host,
		"resource":  event.InvolvedObject.Kind,
	}
}

// DeletePattern removes a pattern by its name
func (pm *matcher) DeletePattern(name string) {
	delete(pm.patterns, name)
}

// GetPatternsByCategory returns patterns filtered by category
func (pm *matcher) GetPatternsByCategory(category string) []sourcePattern {
	var results []sourcePattern
	for _, pattern := range pm.patterns {
		if pattern.Category == category {
			results = append(results, pattern)
		}
	}
	return results
}

// CommonPatterns returns a slice of predefined sourcePattern structures
// These patterns are used for various event matching scenarios
func CommonPatterns() []sourcePattern {
	return []sourcePattern{
		// Emergency event patterns for quick filtering
		{
			Name:        "critical_node_network_failure",
			Description: "Critical node network connectivity failure",
			Category:    "emergency",
			Severity:    "critical",
			Components:  []string{"node", "network"},
			Match: func(event *corev1.Event) bool {
				return matchesAll(event, map[string]string{
					"type":     "Warning",
					"reason":   "NodeNetworkUnavailable",
					"source":   "kubelet",
					"severity": "critical",
				})
			},
		},
		{
			Name:        "service_total_outage",
			Description: "Complete service connectivity loss",
			Category:    "emergency",
			Severity:    "critical",
			Components:  []string{"service", "endpoint"},
			Match: func(event *corev1.Event) bool {
				return matchesAll(event, map[string]string{
					"type":   "Warning",
					"reason": "ServiceEndpointNotReady",
				})
			},
		},

		// Smart filtering patterns to reduce noise
		{
			Name:        "transient_connection_reset",
			Description: "Temporary connection reset that usually self-recovers",
			Category:    "filter",
			Severity:    "low",
			Components:  []string{"connection"},
			Match: func(event *corev1.Event) bool {
				return matchesAll(event, map[string]string{
					"reason": "ConnectionReset",
				}) && isTransient(event)
			},
		},
		{
			Name:        "routine_dns_retry",
			Description: "Normal DNS resolution retry",
			Category:    "filter",
			Severity:    "low",
			Components:  []string{"dns"},
			Match: func(event *corev1.Event) bool {
				return matchesAll(event, map[string]string{
					"type":   "Normal",
					"reason": "DNSRetry",
				}) && event.Count < 3
			},
		},

		// Event classification patterns for the classifier
		{
			Name:        "pod_network_isolation",
			Description: "Pod network policy isolation issue",
			Category:    "classification",
			Severity:    "medium",
			Components:  []string{"networkpolicy", "pod"},
			Match: func(event *corev1.Event) bool {
				return matchesAll(event, map[string]string{
					"reason": "NetworkPolicyFailed",
				}) && strings.Contains(event.Message, "isolation")
			},
		},
		{
			Name:        "load_balancer_health",
			Description: "Load balancer health check failure",
			Category:    "classification",
			Severity:    "medium",
			Components:  []string{"loadbalancer", "service"},
			Match: func(event *corev1.Event) bool {
				return matchesAll(event, map[string]string{
					"type":     "Warning",
					"reason":   "HealthCheckFailed",
					"resource": "Service",
				})
			},
		},

		// Analyzer patterns for deep analysis
		{
			Name:        "cascading_network_failure",
			Description: "Network failure causing cascading effects",
			Category:    "analysis",
			Severity:    "high",
			Components:  []string{"network", "service", "pod"},
			Match: func(event *corev1.Event) bool {
				return hasCascadingEffect(event)
			},
		},
		{
			Name:        "intermittent_connectivity",
			Description: "Pattern of intermittent network connectivity",
			Category:    "analysis",
			Severity:    "medium",
			Components:  []string{"network", "connection"},
			Match: func(event *corev1.Event) bool {
				return hasIntermittentPattern(event)
			},
		},
	}
}

// matchesAll checks if all key-value pairs in the criteria map match the corresponding values in the event map
func matchesAll(event *corev1.Event, criteria map[string]string) bool {
	for k, v := range criteria {
		switch k {
		case "type":
			if event.Type != v {
				return false
			}
		case "reason":
			if event.Reason != v {
				return false
			}
		case "source":
			if event.Source.Component != v {
				return false
			}
		case "namespace":
			if event.Namespace != v {
				return false
			}
		case "name":
			if event.Name != v {
				return false
			}
		case "component":
			if event.Source.Component != v {
				return false
			}
		case "host":
			if event.Source.Host != v {
				return false
			}
		case "message":
			if !strings.Contains(event.Message, v) {
				return false
			}
		case "resource":
			if event.InvolvedObject.Kind != v {
				return false
			}
		default:
			// For other keys, check if the value matches in the event's annotations
			if val := event.Annotations[k]; val != v {
				return false
			}
		}
	}
	return true
}

// isTransient checks if an event is transient based on its duration
func isTransient(event *corev1.Event) bool {
	firstTime := event.FirstTimestamp
	if firstTime.IsZero() {
		// If start time is not available, we can't determine if it's transient
		return false
	}

	// Extract the end time from the event
	lastTime := event.LastTimestamp
	if lastTime.IsZero() {
		// If end time is not available, we can't determine if it's transient
		return false
	}

	// Consider the event transient if it lasted less than 30 seconds
	return lastTime.Sub(firstTime.Time) < 30*time.Second
}

func hasCascadingEffect(event *corev1.Event) bool {
	// TODO: 检查是否存在级联效应
	// 1. 检查相关资源的数量
	// 2. 检查影响范围
	// 3. 检查时间相关性
	return false
}

func hasIntermittentPattern(event *corev1.Event) bool {
	// TODO:检查是否存在间歇性问题模式
	// 1. 检查历史记录中的类似事件
	// 2. 分析事件频率
	// 3. 检查恢复模式
	return false
}
