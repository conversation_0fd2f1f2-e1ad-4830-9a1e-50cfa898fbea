package telemeter

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/config"
	talosctx "code.devops.xiaohongshu.com/cloud-native/talos/pkg/context"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
)

// Telemeter handles Kubernetes event collection and analysis
type Telemeter struct {
	// Configuration for the Telemeter
	config *config.TelemeterConfig
}

// New creates a new Telemeter instance
func New(ctx context.Context, cfg *config.TelemeterConfig) (*Telemeter, error) {
	// Initialize background tasks if telemetry is enabled
	if cfg.Enable {
		// Create edge for data collection
		edge, err := newEdge(ctx, cfg.Source)
		if err != nil {
			return nil, fmt.Errorf("failed to create edge: %w", err)
		}

		background := &Background{
			config:          cfg,
			edge:            edge,
			cache:           newSourceCache(cfg.CacheTTL),
			statistics:      newStatistics(),
			matcher:         newMatcher(),
			smartFilters:    newFilter(cfg.Filter),
			emergenceFilter: newFilter(cfg.Filter),
			Alerter:         newAlerter(cfg.Alerter),
			Anonymizer:      newAnonymizer(),
			handler:         newHandler(),
		}

		talosctx.SafeGo(ctx, "edge_collection", func() { background.startEdgeCollection(ctx) })
		talosctx.SafeGo(ctx, "cache_cleanup", func() { background.startCacheCleanup(ctx) })
	}
	return &Telemeter{config: cfg}, nil
}

// Background represents the background tasks for event collection and analysis
type Background struct {
	// Configuration for background tasks
	config *config.TelemeterConfig
	// Edge component for data collection
	edge Edge
	// Cache for storing and retrieving data
	cache Cache
	// Statistics component for data analysis
	statistics Statistics
	// Matcher for pattern recognition
	matcher Matcher
	// Filter for smart event filtering
	smartFilters Filter
	// Filter for emergency event detection
	emergenceFilter Filter
	// Alerter for sending notifications
	Alerter Alerter
	// Anonymizer for data privacy
	Anonymizer Anonymizer
	// Handler for processing events
	handler Handler
}

// startEdgeCollection starts the periodic event collection
func (b *Background) startEdgeCollection(ctx context.Context) {
	// Create a ticker for periodic event collection
	ticker := time.NewTicker(b.config.SyncInterval)
	defer ticker.Stop()

	// Run event collection loop
	for {
		select {
		case <-ctx.Done():
			// Exit if context is cancelled
			return
		case <-ticker.C:
			// Collect and process events on each tick
			if err := b.collectAndProcessEvents(ctx); err != nil {
				// Log error but continue with other sources
				slog.Error("Error collecting data", "error", err)
			}
		}
	}
}

// collectAndProcessEvents collects and processes events from all sources
func (b *Background) collectAndProcessEvents(ctx context.Context) error {
	// Collect data from all sources
	for _, source := range b.edge.GetSources() {
		args := map[string]any{
			// TODO: add args
		}
		results, err := source.Collect(ctx, args)
		if err != nil {
			// Log error but continue with other sources
			slog.Error("Error collecting data", "error", err)
			continue
		}

		var events []*corev1.Event
		for _, result := range results {
			event := &corev1.Event{}
			if err := runtime.DefaultUnstructuredConverter.
				FromUnstructured(result.UnstructuredContent(), event); err != nil {
				// Log error but continue with other results
				slog.Error("Error converting to Event", "error", err)
				continue
			}
			events = append(events, event)
		}

		// Filter emergency events
		emergencyData := b.emergenceFilter.Filter(events)
		if len(emergencyData) > 0 {
			// Alert on emergency events
			if err := b.Alerter.Alert(ctx, emergencyData); err != nil {
				slog.Error("Error alerting emergency events", "error", err)
			}
		}
		// Filter normal events
		normalData := b.smartFilters.Filter(events)

		// Anonymize data
		normalData = b.Anonymizer.Anonymize(normalData)

		// Analyze data
		analysisResults := b.handler.Handle(normalData)

		// Update cache with analysis results only
		if analysisResults != nil {
			b.cache.Update("analysis_results", analysisResults)
		}
	}

	// Return nil to indicate successful completion
	return nil
}

// startCacheCleanup starts the periodic cache cleanup
func (b *Background) startCacheCleanup(ctx context.Context) {
	// Create a ticker for periodic cleanup
	ticker := time.NewTicker(b.config.CleanupInterval)
	defer ticker.Stop()

	// Run cleanup loop
	for {
		select {
		case <-ctx.Done():
			// Exit if context is cancelled
			return
		case <-ticker.C:
			// Perform cache cleanup on each tick
			b.cache.Cleanup()
		}
	}
}
