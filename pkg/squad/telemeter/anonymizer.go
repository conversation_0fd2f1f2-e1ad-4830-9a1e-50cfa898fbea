package telemeter

import corev1 "k8s.io/api/core/v1"

// Anonymizer defines the interface for anonymizing event data
type Anonymizer interface {
	// Anonymize takes a slice of Kubernetes events and returns an anonymized version
	Anonymize(event []*corev1.Event) []*corev1.Event
}

// Ensure anonymizer implements Anonymizer interface
var _ Anonymizer = (*anonymizer)(nil)

// anonymizer struct implements the Anonymizer interface
type anonymizer struct {
}

// newAnonymizer creates and returns a new Anonymizer instance
func newAnonymizer() Anonymizer {
	return &anonymizer{}
}

// Anonymize implements the Anonymizer interface.
// Currently, it returns the data unchanged. This method should be implemented
// to perform actual anonymization of sensitive information in the events.
func (a *anonymizer) Anonymize(data []*corev1.Event) []*corev1.Event {
	return data
}
