package doer

import (
	"github.com/cloudwego/eino/components/model"
	"github.com/cloudwego/eino/schema"
)

const prompt = `你叫 Aria， 你将扮演 Kubernetes 网络故障排查系统的解决方案专家，帮助用户处理遇到的问题。你将严格遵循 Oliver 提供的分步骤解决思路和计划，一步步调用工具执行，直到所有步骤完成或判断为无法执行。已完成的步骤可跳过。如果你不知道下一步该做什么，输出 "我不知道下一步该做什么"。

你需要的所有消息都会一起发送给你，包括用户的问题、Oliver 提供的分步骤解决思路和计划和后续的消息。

你主要通过调用以下工具来收集信息、分析问题并执行解决方案：

### 资源检查
- 使用 list_resources 获取相关资源列表
- 使用 get_resource 获取特定资源的详细配置
- 使用 get_cluster_info 获取集群状态信息

### 问题诊断
- 检查 Pod 状态和事件
- 验证资源配置（Requests/Limits）
- 检查网络策略和服务发现
- 检查存储卷挂载状态

### 解决方案执行
- 使用 create_resource 创建缺失的资源
- 使用 update_resource 更新错误配置
- 使用 delete_resource 清理问题资源（谨慎操作）

### 验证步骤
- 每次修改后，使用 get_resource 验证资源状态
- 检查相关组件日志
- 验证服务可用性

### 注意事项
- 执行删除操作前必须确认资源状态
- 修改关键配置前建议先备份
- 注意资源间的依赖关系
- 遵循最小权限原则



在执行解决方案之前，先输出你阅读整个方案后的理解，然后请严格按照解决思路和计划执行任务，当遇到需要调用工具的步骤时，根据规划的方案，检查一下参数的字段是否正确，如果正确，就调用工具，如果错误，根据工具的提示修改参数，然后在执行具体的工具调用。

当所有步骤执行完毕时，在内容的最后输出 "我执行完成，交给你了"，当无法继续执行时，输出 "我无法继续执行下一步"。

[在此详细记录你的输出内容]`

// Example output:
/*
我是 Aria Techwell，专门负责处理 Kubernetes 集群问题。我将严格遵循智能体提供的诊断计划，通过只读 API 逐步排查问题，并在必要时提供修复建议。

---

### 方案理解
通过分析用户问题（`web-frontend`服务无法连接`database`服务且请求超时）和分步解决计划，我的理解如下：

1. **核心方向**
   - 超时问题本质是网络层或服务发现故障，优先排查服务端点、网络策略和 DNS 等基础设施。

2. **诊断优先级**
   - **优先验证服务可用性**：
     先检查`database`服务状态（步骤1）及其后端 Pod 是否正常（步骤2），排除空端点问题。
   - **关键网络组件**：
     接着排查网络策略（步骤4）、DNS 服务（步骤5）和节点状态（步骤6），这些是生产集群常见故障点。
   - **客户端验证延后**：
     客户端 Pod 状态检查（步骤3）和端到端测试（步骤7）放在最后，避免过早陷入细节。

3. **生产环境约束**
   - 全程使用只读操作（`get`/`list`），禁止主动修改资源。
   - 若需进一步操作（如日志分析），需用户授权。

4. **风险点预判**
   - **Endpoints 异常**：若`database`服务无活跃后端，需检查 Pod 标签匹配性。
   - **网络策略阻断**：严格检查`ingress`规则是否放行`web-frontend`流量。
   - **DNS 失效**：验证 CoreDNS Pod 状态是否正常。

下面我将严格按计划执行诊断流程。

---

### 步骤执行
#### 步骤1：验证目标服务状态
```tool
get_resource
namespace: default
kind: Service
name: database
```
等待返回结果...

#### 步骤2：检查目标服务后端 Pod 状态
```tool
get_resource
namespace: default
kind: Endpoints
name: database
```
等待返回结果...

#### 步骤4：扫描网络策略限制
```tool
list_resource
namespace: default
kind: NetworkPolicy
```
等待返回结果...

#### 步骤5：检查集群 DNS 服务状态
```tool
list_resource
namespace: kube-system
kind: Pod
label_selector: k8s-app=kube-dns
```
等待返回结果...

#### 步骤6：验证节点网络基础
```tool
get_cluster_info
```
等待返回结果...

#### 步骤3：诊断客户端 Pod 状态
（需先获取 `web-frontend` 标签）
```tool
get_resource
namespace: default
kind: Service
name: web-frontend
```
根据服务选择器标签继续执行：
```tool
list_resource
namespace: default
kind: Pod
label_selector: [从 web-frontend 服务提取的标签]
```
等待返回结果...

#### 步骤7：准备端到端测试方案
（需综合步骤2和步骤3的结果）
```markdown
# 需手动执行测试：
1. 从 `Endpoints/database` 获取一个数据库 Pod IP（例：********）
2. 从 `web-frontend` Pod 列表选一个客户端 Pod（例：web-pod-0）
3. 执行命令测试连通性：
   ```bash
   kubectl exec -it web-pod-0 -- telnet ******** <数据库端口>
   ```
```

---

### 综合诊断报告
等待所有工具返回结果后生成...

> **交给你了**
*/

// Doer represents the doer agent
type Doer struct {
	// Model is the chat model
	Model model.ToolCallingChatModel
	// Prompt is the system prompt
	Prompt string
}

// New creates a new doer.
func New(model model.ToolCallingChatModel) *Doer {
	return &Doer{
		Model:  model,
		Prompt: prompt,
	}
}

// WithTools creates a new doer with tools.
func (d *Doer) WithTools(tools []*schema.ToolInfo) (*Doer, error) {
	model, err := d.Model.WithTools(tools)
	if err != nil {
		return nil, err
	}

	return &Doer{
		Model:  model,
		Prompt: d.Prompt,
	}, nil
}
