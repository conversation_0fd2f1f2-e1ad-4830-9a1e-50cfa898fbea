package state

import (
	"context"

	"github.com/cloudwego/eino/schema"
)

// State defines the interface for managing message state.
type State interface {
	// GetMessages returns all stored messages.
	GetMessages() []*schema.Message
	// AddMessage adds a new message to the state.
	AddMessage(message *schema.Message)
}

var _ State = (*state)(nil)

type state struct {
	messages []*schema.Message // List of messages
}

func NewState(ctx context.Context) State {
	return &state{}
}

func (state *state) GetMessages() []*schema.Message {
	return state.messages
}

func (state *state) AddMessage(message *schema.Message) {
	state.messages = append(state.messages, message)
}

// AddMessageBeforeHandle updates the state with the input message and returns the input message. It is used as a pre-handler for nodes.
func AddMessageBeforeHandle(ctx context.Context, input *schema.Message, localState State) (*schema.Message, error) {
	localState.AddMessage(input)
	return input, nil
}
