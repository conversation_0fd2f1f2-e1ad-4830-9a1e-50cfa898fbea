package rag

import (
	"context"
	"fmt"
	"time"

	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/config"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/squad/state"
	"github.com/cloudwego/eino/components/retriever"
	"github.com/cloudwego/eino/schema"
)

// Retriever defines the interface for knowledge retrieval.
// This interface is used to perform knowledge retrieval based on the provided input messages.
// It also embeds the retriever.Retriever interface, which provides the actual retrieval logic.
type Retriever interface {
	retriever.Retriever
	// Name returns the name of the retriever
	Name() string
}

// RetrieveOption defines options for retrieval.
// These options are used to configure the retrieval process.
type RetrieveOption struct {
	// Number of results to return
	TopK int
	// Minimum similarity score threshold
	MinScore float64
	// Maximum tokens to return
	MaxTokens int
	// Temperature for relevance scoring
	Temperature float64
}

// RetrieverFactory defines the interface for creating retrievers.
// This interface is used to create new retriever instances based on the provided configuration.
type RetrieverFactory interface {
	// Create creates a new retriever instance based on the provided configuration
	Create(ctx context.Context, config *config.KnowledgeSourceConfig) (Retriever, error)
}

// registry maintains a map of registered retriever factories.
// This map is used to store retriever factories by their names.
var registry = make(map[string]RetrieverFactory)

// RegisterRetriever registers a new retriever factory under the given name
func RegisterRetriever(name string, factory RetrieverFactory) {
	registry[name] = factory
}

// NewRetriever creates a new retriever based on the provided configuration
func NewRetriever(ctx context.Context, config *config.KnowledgeSourceConfig) (Retriever, error) {
	// Check if the configuration is provided
	if config == nil {
		return nil, fmt.Errorf("knowledge source configuration is required")
	}

	// Check if the configuration type is registered in the registry
	factory, exists := registry[config.Type]
	if !exists {
		return nil, fmt.Errorf("unsupported retriever type: %s", config.Type)
	}

	return factory.Create(ctx, config)
}

// PreHandler updates the state with the input message and builds RAG context if needed.
func PreHandler(ctx context.Context, input *schema.Message, localState state.State) (*schema.Message, error) {
	// Store the message in state
	localState.AddMessage(input)

	// If we have analysis results, build RAG context
	if analysis, ok := input.Extra["analysis_results"].(map[string]any); ok {
		ragContext := buildRAGContext(analysis)
		// Store RAG context in the message for retriever to use
		input.Extra["rag_context"] = ragContext
	}

	return input, nil
}

// buildRAGContext constructs a context map for Retrieval-Augmented Generation (RAG)
// based on the provided analysis data.
func buildRAGContext(analysis map[string]any) map[string]any {
	// Initialize an empty context map
	context := make(map[string]any)
	// Define a list of relevant fields to extract from the analysis
	relevantFields := []string{
		"error_type",
		"component",
		"patterns",
		"severity",
		"affected_resources",
		"error_messages",
	}

	// Iterate through relevant fields and add them to the context if present
	for _, field := range relevantFields {
		if value, ok := analysis[field]; ok {
			context[field] = value
		}
	}

	// If a timestamp is present, create a time range for the last 24 hours
	if timestamp, ok := analysis["timestamp"].(time.Time); ok {
		context["time_range"] = map[string]time.Time{
			"start": timestamp.Add(-24 * time.Hour),
			"end":   timestamp,
		}
	}

	// Return the constructed context map
	return context
}

// PostHandler updates the state with the output message.
func PostHandler(ctx context.Context, input *schema.Message, localState state.State) (*schema.Message, error) {
	// TODO: Implement post-processing logic if needed
	localState.AddMessage(input)
	return input, nil
}
