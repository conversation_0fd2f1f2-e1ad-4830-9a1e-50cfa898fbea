package dify

import (
	"context"
	"fmt"

	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/config"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/squad/rag"
	"github.com/cloudwego/eino-ext/components/retriever/dify"
	"github.com/cloudwego/eino/components/retriever"
	"github.com/cloudwego/eino/schema"
)

const name = "dify"

func init() {
	// Register the Dify retriever factory
	rag.RegisterRetriever(name, &Factory{})
}

// Factory creates Dify retrievers
type Factory struct{}

// Create creates a new Dify retriever instance based on the provided configuration
func (f *Factory) Create(ctx context.Context, config *config.KnowledgeSourceConfig) (rag.Retriever, error) {
	// Check if Dify configuration is provided
	if config.Dify == nil {
		return nil, fmt.Errorf("dify configuration is required")
	}

	// Create Dify retriever configuration
	cfg := &dify.RetrieverConfig{
		APIKey:    config.Dify.DatasetAPIKey,
		Endpoint:  config.Dify.Endpoint,
		DatasetID: config.Dify.DatasetID,
	}

	// Initialize new Dify retriever
	retriever, err := dify.NewRetriever(ctx, cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create dify client: %w", err)
	}

	// Return new Retriever instance
	return &Retriever{retriever: retriever}, nil
}

// Ensure Retriever implements the Retriever interface
var _ rag.Retriever = (*Retriever)(nil)

// Retriever implements the Retriever interface for Dify
type Retriever struct {
	// Embed the Dify retriever
	retriever *dify.Retriever
}

// Retrieve performs knowledge retrieval based on the input messages
func (r *Retriever) Retrieve(ctx context.Context, input string, opts ...retriever.Option) ([]*schema.Document, error) {
	// Check if context is canceled or deadline exceeded
	if err := ctx.Err(); err != nil {
		return nil, fmt.Errorf("context error: %w", err)
	}

	// Perform retrieval using the Dify retriever
	return r.retriever.Retrieve(ctx, input, opts...)
}

// Name returns the name of the retriever
func (r *Retriever) Name() string {
	return name
}
