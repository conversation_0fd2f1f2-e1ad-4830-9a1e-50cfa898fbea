package allin

import (
	"context"
	"fmt"

	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/components/retriever/allin"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/config"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/squad/rag"
	"github.com/cloudwego/eino/components/retriever"
	"github.com/cloudwego/eino/schema"
)

const name = "allin"

func init() {
	// Register the AllIn retriever factory
	rag.RegisterRetriever(name, &Factory{})
}

// Factory creates AllIn retrievers
type Factory struct{}

// Create creates a new AllIn retriever instance based on the provided configuration
func (f *Factory) Create(ctx context.Context, config *config.KnowledgeSourceConfig) (rag.Retriever, error) {
	// Check if AllIn configuration is provided
	if config.Allin == nil {
		return nil, fmt.Errorf("allin configuration is required")
	}

	// Create AllIn retriever configuration
	cfg := &allin.RetrieverConfig{
		Endpoint: config.Allin.Endpoint,
		APPKey:   config.Allin.APPKey,
		APPID:    config.Allin.APPID,
	}

	// Initialize new AllIn retriever
	retriever, err := allin.NewRetriever(ctx, cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create allin retriever: %w", err)
	}

	// Return new Retriever instance
	return &Retriever{retriever: retriever}, nil
}

// Ensure Retriever implements the Retriever interface
var _ rag.Retriever = (*Retriever)(nil)

// Retriever implements the Retriever interface for AllIn
type Retriever struct {
	// Embed the AllIn retriever
	retriever *allin.Retriever
}

// Retrieve performs knowledge retrieval based on the input messages
func (r *Retriever) Retrieve(ctx context.Context, input string, opts ...retriever.Option) ([]*schema.Document, error) {
	// Check if context is canceled or deadline exceeded
	if err := ctx.Err(); err != nil {
		return nil, fmt.Errorf("context error: %w", err)
	}

	// Perform retrieval using the AllIn retriever
	return r.retriever.Retrieve(ctx, input, opts...)
}

// Name returns the name of the retriever
func (r *Retriever) Name() string {
	return name
}
