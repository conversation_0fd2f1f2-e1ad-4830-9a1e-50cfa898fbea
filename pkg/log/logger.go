package log

import (
	"log/slog"
	"os"
	"time"
)

// NewLogger creates a new slog.Logger with custom configuration
func NewLogger(level int) *slog.Logger {
	// Create a new JSON handler for logging to stdout
	handler := slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{
		// Include source file and line number in log output
		AddSource: true,
		// Custom attribute replacement function
		ReplaceAttr: func(groups []string, a slog.Attr) slog.Attr {
			// Format timestamp as RFC3339
			if a.Key == slog.TimeKey {
				return slog.Attr{
					Key:   slog.TimeKey,
					Value: slog.StringValue(a.Value.Time().Format(time.RFC3339)),
				}
			}
			return a
		},
		// Set log level based on input parameter
		Level: slog.Level(level),
	})

	// Create and return a new logger with the configured handler
	return slog.New(handler)
}
