package config

import (
	"context"
	"log/slog"
	"strings"
	"time"

	"github.com/joho/godotenv"
	"github.com/spf13/viper"
)

// LoadConfig loads all configuration from multiple sources with the following priority
// (from lowest to highest):
//
//  1. Default values (lowest priority)
//     Set via setDefaultConfig function
//     Example: Squad.LLMs.Allin.Model = "deepseek-r1"
//
//  2. .env file (if exists)
//     Used mainly for local development
//     Example .env content:
//     TALOS_SQUAD_LLMS_ALLIN_MODEL=model-from-env-file
//
//  3. Environment variables (highest priority)
//     Will override all other sources
//     Must be prefixed with "TALOS_"
//     Uses "_" as delimiter for nested keys
//     Example: export TALOS_SQUAD_LLMS_ALLIN_MODEL=model-from-system-env
//
// Environment variable naming rules:
//   - Prefix: TALOS_
//   - Delimiter: _ (underscore)
//   - Case: UPPER_CASE
//   - Example: Squad.LLMs.Allin.Model -> TALOS_SQUAD_LLMS_ALLIN_MODEL
//
// Usage example:
//
// default value
// Squad.LLMs.Allin.Model = "deepseek-r1"

// .env file setting
// TALOS_SQUAD_LLMS_ALLIN_MODEL=model-from-env-file

// system environment variable setting (highest priority)
// export TALOS_SQUAD_LLMS_ALLIN_MODEL=model-from-system-env
func LoadConfig() (*Config, error) {
	// Load .env file if exists
	if err := godotenv.Load(); err != nil {
		slog.LogAttrs(context.Background(), slog.LevelWarn,
			".env file not found", slog.Any("error", err))
	}

	v := viper.New()
	// Set default values
	setDefaultConfig(v)
	// Use TALOS_ as prefix for environment variables
	v.SetEnvPrefix("TALOS")
	// Use _ as delimiter in environment variables
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	// Tell viper to use environment variables
	v.AutomaticEnv()

	// Initialize config struct
	config := &Config{}

	// Unmarshal config
	if err := v.Unmarshal(config); err != nil {
		return nil, err
	}

	slog.Info("Configuration loaded successfully")
	return config, nil
}

// setDefaultConfig sets default values for all configuration options
func setDefaultConfig(v *viper.Viper) {
	// Base Configuration
	v.SetDefault("GinMode", "debug")

	// Allin Configuration
	v.SetDefault("Squad.LLMs.Allin.BaseURL", "http://redservingapi.devops.xiaohongshu.com/v1")
	v.SetDefault("Squad.LLMs.Allin.APIKey", "")
	v.SetDefault("Squad.LLMs.Allin.Model", "deepseek-r1-0528")
	v.SetDefault("Squad.LLMs.Allin.Temperature", 0.7)
	v.SetDefault("Squad.LLMs.Allin.MaxTokens", 8192)

	// Ollama Configuration
	v.SetDefault("Squad.LLMs.Ollama.BaseURL", "http://localhost:11434")
	v.SetDefault("Squad.LLMs.Ollama.Model", "qwen3:4b")

	// MCP Configuration
	v.SetDefault("Squad.MCP.BaseUrl", "http://talos-mcp:8080/sse")

	// Telemeter Configuration
	v.SetDefault("Squad.Telemeter.Background.Enable", false)
	v.SetDefault("Squad.Telemeter.Background.SyncInterval", 15*time.Minute)
	v.SetDefault("Squad.Telemeter.Background.CleanupInterval", 5*time.Minute)
	v.SetDefault("Squad.Telemeter.Background.CacheTTL", 15*time.Minute)

	// Source Configuration
	v.SetDefault("Squad.Telemeter.Background.Source.EnableEvents", false)
	v.SetDefault("Squad.Telemeter.Background.Source.EnableLogs", false)
	v.SetDefault("Squad.Telemeter.Background.Source.EnableMetrics", false)
	v.SetDefault("Squad.Telemeter.Background.Source.EnableTraces", false)

	// Filter Configuration
	v.SetDefault("Squad.Telemeter.Background.Filter.EventTypes", []string{"Warning"})
	v.SetDefault("Squad.Telemeter.Background.Filter.NetworkFilters", []string{"NetworkNotReady"})

	// Alerter Configuration
	v.SetDefault("Squad.Telemeter.Background.Alerter.AlertThreshold", 3)

	// Knowledge Source Configuration
	v.SetDefault("Squad.KnowledgeSource.Type", "allin")
	v.SetDefault("Squad.KnowledgeSource.Allin.Endpoint", "https://aiplat-gateway.devops.beta.xiaohongshu.com")
	v.SetDefault("Squad.KnowledgeSource.Allin.APPKey", "")
	v.SetDefault("Squad.KnowledgeSource.Allin.APPID", "knowledge4talos")
}
