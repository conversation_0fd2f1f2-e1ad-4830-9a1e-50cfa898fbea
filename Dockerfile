FROM docker-reg.devops.xiaohongshu.com/shequ/sns-gosdk-ci:latest

# Build stage
FROM docker-reg.devops.xiaohongshu.com/shequ/golang:1.24.2 AS builder

ARG TARGETOS
ARG TARGETARCH
ARG GIT_COMMIT=unknown

# Set working directory
WORKDIR /app

COPY --from=0 /root/.ssh/* /root/.ssh/
RUN chmod 600 /root/.ssh/*
RUN git config --global http.extraheader "PRIVATE-TOKEN: zew7xwK7Hyfzco1W66Sn"
RUN git config --global url."*******************************:".insteadof "http://code.devops.xiaohongshu.com/"
RUN go env -w GOSUMDB=off
RUN go env -w GOPROXY=https://goproxy.cn,direct

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=${TARGETOS:-linux} GOARCH=${TARGETARCH} \
    go build -mod=mod \
    -ldflags "-X main.gitCommit=${GIT_COMMIT}" \
    -o talos main.go

# Final stage
FROM docker-reg.devops.xiaohongshu.com/shequ/distroless/static:nonroot

# Set working directory
WORKDIR /app

# Copy binary from builder
COPY --from=builder /app/talos /app/talos

# Create non-root user
USER 65532:65532

ENTRYPOINT ["/app/talos"]