variables:
  IMAGE_NAME: docker-reg.devops.xiaohongshu.com/shequ/talos
  FRONTEND_IMAGE_NAME: docker-reg.devops.xiaohongshu.com/shequ/talos-frontend

stages:
  - build

docker-image:
  stage: build
  image: docker-reg.devops.xiaohongshu.com/library/docker:v1.0
  before_script:
    - mkdir -p ~/.docker
    - echo -n "Xiaohongshu@123" | docker login docker-reg.devops.xiaohongshu.com --username shequfaas --password-stdin
  script:
    - IMAGE_TAG=$IMAGE_NAME:${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHA:0:8} && echo $IMAGE_TAG
    - docker build --build-arg GIT_COMMIT=${CI_COMMIT_SHA:0:8} -t $IMAGE_TAG -f Dockerfile .
    - docker push $IMAGE_TAG
  retry: 2
  only:
    - tags

frontend-docker-image:
  stage: build
  image: docker-reg.devops.xiaohongshu.com/library/docker:v1.0
  before_script:
    - mkdir -p ~/.docker
    - echo -n "Xiao<PERSON><PERSON>@123" | docker login docker-reg.devops.xiaohongshu.com --username shequfaas --password-stdin
  script:
    - FRONTEND_IMAGE_TAG=$FRONTEND_IMAGE_NAME:${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHA:0:8} && echo $FRONTEND_IMAGE_TAG
    - docker build -t $FRONTEND_IMAGE_TAG -f frontend/Dockerfile frontend/
    - docker push $FRONTEND_IMAGE_TAG
  retry: 2
  only:
    - tags
