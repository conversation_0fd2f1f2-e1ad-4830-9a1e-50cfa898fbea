package service

import (
	"context"

	taloscallbacks "code.devops.xiaohongshu.com/cloud-native/talos/pkg/components/callbacks"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/squad"
	"github.com/cloudwego/eino/compose"
	"github.com/cloudwego/eino/flow/agent"
	"github.com/cloudwego/eino/schema"
)

// ResearchService represents the research service that interacts with the squad interface
type ResearchService struct {
	squad squad.Interface
}

// NewResearchService creates a new instance of ResearchService
func NewResearchService(squad squad.Interface) *ResearchService {
	return &ResearchService{squad: squad}
}

// Request represents the structure of a generate request
type Request struct {
	Content string `json:"content,omitempty"`
}

// Stream handles streaming research processing
func (s *ResearchService) Stream(ctx context.Context, req Request) (*schema.StreamReader[*schema.Message], error) {
	// Create the message
	messages := []*schema.Message{
		{
			Role:    schema.User,
			Content: req.Content,
		},
	}

	// 使用新的日志处理器实例
	logHandler := taloscallbacks.NewLogHandler()
	return s.squad.Stream(ctx, messages,
		agent.WithComposeOptions(compose.WithCallbacks(logHandler.CallBackHandler())),
	)
}

// StreamWithCallbacks 使用指定的回调处理器进行流式研究处理
func (s *ResearchService) StreamWithCallbacks(ctx context.Context, req Request, logHandler *taloscallbacks.LogHandler, responseHandler *taloscallbacks.ResponseHandler) (*schema.StreamReader[*schema.Message], error) {
	// Create the message
	messages := []*schema.Message{
		{
			Role:    schema.User,
			Content: req.Content,
		},
	}

	// 暂时只使用响应处理器，日志处理器单独处理
	combinedHandler := responseHandler.CallBackHandler()

	return s.squad.Stream(ctx, messages,
		agent.WithComposeOptions(compose.WithCallbacks(combinedHandler)),
	)
}
