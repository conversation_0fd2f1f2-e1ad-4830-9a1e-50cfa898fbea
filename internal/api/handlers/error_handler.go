package handlers

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"runtime/debug"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// ErrorType 错误类型枚举
type ErrorType string

const (
	ErrorTypeValidation   ErrorType = "validation"
	ErrorTypeTimeout      ErrorType = "timeout"
	ErrorTypeResource     ErrorType = "resource"
	ErrorTypeNetwork      ErrorType = "network"
	ErrorTypeInternal     ErrorType = "internal"
	ErrorTypeClientGone   ErrorType = "client_gone"
	ErrorTypeCancellation ErrorType = "cancellation"
)

// StreamError 流处理错误
type StreamError struct {
	Type      ErrorType              `json:"type"`
	Message   string                 `json:"message"`
	Code      string                 `json:"code"`
	Timestamp time.Time              `json:"timestamp"`
	RequestID string                 `json:"request_id,omitempty"`
	Details   map[string]interface{} `json:"details,omitempty"`
}

func (e *StreamError) Error() string {
	return fmt.Sprintf("[%s] %s: %s", e.Type, e.Code, e.Message)
}

// ErrorHandler 错误处理器
type ErrorHandler struct {
	// 错误统计
	errorCounts map[ErrorType]int64
	mu          sync.RWMutex

	// 恢复机制
	retryAttempts map[string]int
	retryMu       sync.Mutex
	maxRetries    int

	// 错误回调
	onError func(*StreamError)
}

// NewErrorHandler 创建新的错误处理器
func NewErrorHandler() *ErrorHandler {
	return &ErrorHandler{
		errorCounts:   make(map[ErrorType]int64),
		retryAttempts: make(map[string]int),
		maxRetries:    3,
	}
}

// HandleError 处理错误并返回适当的响应
func (eh *ErrorHandler) HandleError(c *gin.Context, err error, requestID string) {
	streamErr := eh.classifyError(err, requestID)

	// 记录错误统计
	eh.recordError(streamErr.Type)

	// 记录错误日志
	eh.logError(streamErr, err)

	// 调用错误回调
	if eh.onError != nil {
		eh.onError(streamErr)
	}

	// 发送错误响应
	eh.sendErrorResponse(c, streamErr)
}

// HandlePanic 处理panic并恢复
func (eh *ErrorHandler) HandlePanic(c *gin.Context, requestID string) {
	if r := recover(); r != nil {
		// 获取堆栈信息
		stack := debug.Stack()

		// 创建panic错误
		streamErr := &StreamError{
			Type:      ErrorTypeInternal,
			Message:   "服务器内部错误",
			Code:      "PANIC_RECOVERED",
			Timestamp: time.Now(),
			RequestID: requestID,
			Details: map[string]interface{}{
				"panic_value": fmt.Sprintf("%v", r),
				"stack_trace": string(stack),
			},
		}

		// 记录panic
		slog.Error("捕获到panic",
			"panic", r,
			"request_id", requestID,
			"stack", string(stack),
		)

		// 处理错误
		eh.recordError(ErrorTypeInternal)
		if eh.onError != nil {
			eh.onError(streamErr)
		}
		eh.sendErrorResponse(c, streamErr)
	}
}

// classifyError 分类错误
func (eh *ErrorHandler) classifyError(err error, requestID string) *StreamError {
	streamErr := &StreamError{
		Timestamp: time.Now(),
		RequestID: requestID,
		Details:   make(map[string]interface{}),
	}

	// 根据错误类型分类
	switch {
	case errors.Is(err, context.Canceled):
		streamErr.Type = ErrorTypeCancellation
		streamErr.Message = "请求已取消"
		streamErr.Code = "REQUEST_CANCELLED"

	case errors.Is(err, context.DeadlineExceeded):
		streamErr.Type = ErrorTypeTimeout
		streamErr.Message = "请求超时"
		streamErr.Code = "REQUEST_TIMEOUT"

	case isNetworkError(err):
		streamErr.Type = ErrorTypeNetwork
		streamErr.Message = "网络连接错误"
		streamErr.Code = "NETWORK_ERROR"

	case isValidationError(err):
		streamErr.Type = ErrorTypeValidation
		streamErr.Message = "请求参数错误"
		streamErr.Code = "VALIDATION_ERROR"

	case isResourceError(err):
		streamErr.Type = ErrorTypeResource
		streamErr.Message = "资源不足"
		streamErr.Code = "RESOURCE_EXHAUSTED"

	default:
		streamErr.Type = ErrorTypeInternal
		streamErr.Message = "服务器内部错误"
		streamErr.Code = "INTERNAL_ERROR"
	}

	// 添加原始错误信息到详情
	streamErr.Details["original_error"] = err.Error()

	return streamErr
}

// recordError 记录错误统计
func (eh *ErrorHandler) recordError(errorType ErrorType) {
	eh.mu.Lock()
	defer eh.mu.Unlock()
	eh.errorCounts[errorType]++
}

// logError 记录错误日志
func (eh *ErrorHandler) logError(streamErr *StreamError, originalErr error) {
	slog.Error("流处理错误",
		"type", streamErr.Type,
		"code", streamErr.Code,
		"message", streamErr.Message,
		"request_id", streamErr.RequestID,
		"original_error", originalErr.Error(),
	)
}

// sendErrorResponse 发送错误响应
func (eh *ErrorHandler) sendErrorResponse(c *gin.Context, streamErr *StreamError) {
	msg := Message{
		Type:    "error",
		Content: streamErr.Message,
	}

	if err := sendSSEMessage(c, msg); err != nil {
		slog.Error("发送错误响应失败", "error", err)
	}
}

// ShouldRetry 判断是否应该重试
func (eh *ErrorHandler) ShouldRetry(requestID string, errorType ErrorType) bool {
	eh.retryMu.Lock()
	defer eh.retryMu.Unlock()

	// 某些错误类型不应该重试
	if errorType == ErrorTypeValidation || errorType == ErrorTypeClientGone {
		return false
	}

	attempts := eh.retryAttempts[requestID]
	if attempts >= eh.maxRetries {
		return false
	}

	eh.retryAttempts[requestID] = attempts + 1
	return true
}

// GetErrorStats 获取错误统计
func (eh *ErrorHandler) GetErrorStats() map[ErrorType]int64 {
	eh.mu.RLock()
	defer eh.mu.RUnlock()

	stats := make(map[ErrorType]int64)
	for k, v := range eh.errorCounts {
		stats[k] = v
	}
	return stats
}

// SetErrorCallback 设置错误回调
func (eh *ErrorHandler) SetErrorCallback(callback func(*StreamError)) {
	eh.onError = callback
}

// WithRecovery 中间件：提供panic恢复
func (eh *ErrorHandler) WithRecovery(requestID string) gin.HandlerFunc {
	return func(c *gin.Context) {
		defer eh.HandlePanic(c, requestID)
		c.Next()
	}
}

// 辅助函数：判断错误类型
func isNetworkError(err error) bool {
	errStr := err.Error()
	return contains(errStr, "connection", "network", "timeout", "refused", "reset")
}

func isValidationError(err error) bool {
	errStr := err.Error()
	return contains(errStr, "invalid", "validation", "required", "format", "parse")
}

func isResourceError(err error) bool {
	errStr := err.Error()
	return contains(errStr, "resource", "limit", "quota", "capacity", "memory", "disk")
}

func contains(str string, substrings ...string) bool {
	for _, substr := range substrings {
		if len(str) >= len(substr) {
			for i := 0; i <= len(str)-len(substr); i++ {
				if str[i:i+len(substr)] == substr {
					return true
				}
			}
		}
	}
	return false
}

// ClientDisconnectionDetector 客户端断开连接检测器
type ClientDisconnectionDetector struct {
	clientGone   <-chan bool
	onDisconnect func()
	ctx          context.Context
	cancel       context.CancelFunc
}

// NewClientDisconnectionDetector 创建客户端断开连接检测器
func NewClientDisconnectionDetector(clientGone <-chan bool, onDisconnect func()) *ClientDisconnectionDetector {
	ctx, cancel := context.WithCancel(context.Background())

	detector := &ClientDisconnectionDetector{
		clientGone:   clientGone,
		onDisconnect: onDisconnect,
		ctx:          ctx,
		cancel:       cancel,
	}

	// 启动检测
	go detector.monitor()

	return detector
}

// monitor 监控客户端连接状态
func (cdd *ClientDisconnectionDetector) monitor() {
	select {
	case <-cdd.clientGone:
		slog.Info("检测到客户端断开连接")
		if cdd.onDisconnect != nil {
			cdd.onDisconnect()
		}
	case <-cdd.ctx.Done():
		return
	}
}

// Stop 停止监控
func (cdd *ClientDisconnectionDetector) Stop() {
	cdd.cancel()
}
