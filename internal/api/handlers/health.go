package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// HealthHandler handles health check requests.
type HealthHandler struct{}

// NewHealthHandler creates a new HealthHandler.
func NewHealthHandler() *HealthHandler {
	return &HealthHandler{}
}

// CheckHealth handles health check requests.
// @Summary 健康检查
// @Description Kubernetes 健康检查探针
// @Tags health
// @Produce text/plain
// @Success 200 {string} string "ok"
// @Router /health [get]
func (h *HealthHandler) CheckHealth(c *gin.Context) {
	c.String(http.StatusOK, "ok")
}
