package handlers

import (
	"context"
	"log/slog"
	"runtime"
	"sync"
	"sync/atomic"
	"time"
)

// ResourceManager 管理系统资源，防止内存泄漏和goroutine泄漏
type ResourceManager struct {
	// 统计信息
	activeGoroutines int64
	activeChannels   int64
	activeRequests   int64

	// 资源限制
	maxGoroutines int64
	maxChannels   int64
	maxRequests   int64

	// 清理管理
	cleanupTasks []func()
	cleanupMu    sync.Mutex

	// 监控
	ticker      *time.Ticker
	stopMonitor chan struct{}
	monitorOnce sync.Once

	// 上下文
	ctx    context.Context
	cancel context.CancelFunc
}

// NewResourceManager 创建新的资源管理器
func NewResourceManager() *ResourceManager {
	ctx, cancel := context.WithCancel(context.Background())

	rm := &ResourceManager{
		maxGoroutines: 1000, // 最大goroutine数量
		maxChannels:   500,  // 最大通道数量
		maxRequests:   100,  // 最大并发请求数量
		stopMonitor:   make(chan struct{}),
		ctx:           ctx,
		cancel:        cancel,
	}

	// 启动资源监控
	rm.startMonitoring()

	return rm
}

// AcquireGoroutine 获取goroutine资源
func (rm *ResourceManager) AcquireGoroutine() bool {
	current := atomic.LoadInt64(&rm.activeGoroutines)
	if current >= rm.maxGoroutines {
		slog.Warn("达到最大goroutine限制", "current", current, "max", rm.maxGoroutines)
		return false
	}

	atomic.AddInt64(&rm.activeGoroutines, 1)
	return true
}

// ReleaseGoroutine 释放goroutine资源
func (rm *ResourceManager) ReleaseGoroutine() {
	atomic.AddInt64(&rm.activeGoroutines, -1)
}

// AcquireChannel 获取通道资源
func (rm *ResourceManager) AcquireChannel() bool {
	current := atomic.LoadInt64(&rm.activeChannels)
	if current >= rm.maxChannels {
		slog.Warn("达到最大通道限制", "current", current, "max", rm.maxChannels)
		return false
	}

	atomic.AddInt64(&rm.activeChannels, 1)
	return true
}

// ReleaseChannel 释放通道资源
func (rm *ResourceManager) ReleaseChannel() {
	atomic.AddInt64(&rm.activeChannels, -1)
}

// AcquireRequest 获取请求资源
func (rm *ResourceManager) AcquireRequest() bool {
	current := atomic.LoadInt64(&rm.activeRequests)
	if current >= rm.maxRequests {
		slog.Warn("达到最大请求限制", "current", current, "max", rm.maxRequests)
		return false
	}

	atomic.AddInt64(&rm.activeRequests, 1)
	return true
}

// ReleaseRequest 释放请求资源
func (rm *ResourceManager) ReleaseRequest() {
	atomic.AddInt64(&rm.activeRequests, -1)
}

// AddCleanupTask 添加清理任务
func (rm *ResourceManager) AddCleanupTask(task func()) {
	rm.cleanupMu.Lock()
	defer rm.cleanupMu.Unlock()
	rm.cleanupTasks = append(rm.cleanupTasks, task)
}

// RunWithTimeout 在超时控制下运行函数
func (rm *ResourceManager) RunWithTimeout(timeout time.Duration, fn func() error) error {
	ctx, cancel := context.WithTimeout(rm.ctx, timeout)
	defer cancel()

	done := make(chan error, 1)

	go func() {
		done <- fn()
	}()

	select {
	case err := <-done:
		return err
	case <-ctx.Done():
		return ctx.Err()
	}
}

// GetStats 获取资源统计信息
func (rm *ResourceManager) GetStats() map[string]interface{} {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	return map[string]interface{}{
		"active_goroutines": atomic.LoadInt64(&rm.activeGoroutines),
		"active_channels":   atomic.LoadInt64(&rm.activeChannels),
		"active_requests":   atomic.LoadInt64(&rm.activeRequests),
		"system_goroutines": runtime.NumGoroutine(),
		"memory_alloc":      m.Alloc,
		"memory_sys":        m.Sys,
		"gc_cycles":         m.NumGC,
	}
}

// startMonitoring 启动资源监控
func (rm *ResourceManager) startMonitoring() {
	rm.monitorOnce.Do(func() {
		rm.ticker = time.NewTicker(30 * time.Second)

		go func() {
			defer rm.ticker.Stop()

			for {
				select {
				case <-rm.ticker.C:
					rm.logResourceStats()
				case <-rm.stopMonitor:
					return
				case <-rm.ctx.Done():
					return
				}
			}
		}()
	})
}

// logResourceStats 记录资源统计信息
func (rm *ResourceManager) logResourceStats() {
	stats := rm.GetStats()

	slog.Info("资源使用统计",
		"active_goroutines", stats["active_goroutines"],
		"active_channels", stats["active_channels"],
		"active_requests", stats["active_requests"],
		"system_goroutines", stats["system_goroutines"],
		"memory_alloc_mb", stats["memory_alloc"].(uint64)/1024/1024,
		"memory_sys_mb", stats["memory_sys"].(uint64)/1024/1024,
		"gc_cycles", stats["gc_cycles"],
	)

	// 检查资源使用是否过高
	if goroutines := stats["system_goroutines"].(int); goroutines > 500 {
		slog.Warn("系统goroutine数量过高", "count", goroutines)
	}

	if memAlloc := stats["memory_alloc"].(uint64); memAlloc > 100*1024*1024 { // 100MB
		slog.Warn("内存使用过高", "alloc_mb", memAlloc/1024/1024)
	}
}

// Close 关闭资源管理器
func (rm *ResourceManager) Close() {
	// 停止监控
	close(rm.stopMonitor)

	// 取消上下文
	rm.cancel()

	// 执行清理任务
	rm.cleanupMu.Lock()
	defer rm.cleanupMu.Unlock()

	for i, task := range rm.cleanupTasks {
		func() {
			defer func() {
				if r := recover(); r != nil {
					slog.Error("清理任务执行失败", "index", i, "panic", r)
				}
			}()
			task()
		}()
	}

	slog.Info("资源管理器已关闭")
}

// SetLimits 设置资源限制
func (rm *ResourceManager) SetLimits(maxGoroutines, maxChannels, maxRequests int64) {
	atomic.StoreInt64(&rm.maxGoroutines, maxGoroutines)
	atomic.StoreInt64(&rm.maxChannels, maxChannels)
	atomic.StoreInt64(&rm.maxRequests, maxRequests)

	slog.Info("资源限制已更新",
		"max_goroutines", maxGoroutines,
		"max_channels", maxChannels,
		"max_requests", maxRequests,
	)
}

// ForceGC 强制执行垃圾回收
func (rm *ResourceManager) ForceGC() {
	before := rm.GetStats()
	runtime.GC()
	after := rm.GetStats()

	slog.Info("强制垃圾回收完成",
		"memory_before_mb", before["memory_alloc"].(uint64)/1024/1024,
		"memory_after_mb", after["memory_alloc"].(uint64)/1024/1024,
	)
}

// 全局资源管理器实例
var globalResourceManager *ResourceManager
var rmOnce sync.Once

// GetGlobalResourceManager 获取全局资源管理器
func GetGlobalResourceManager() *ResourceManager {
	rmOnce.Do(func() {
		globalResourceManager = NewResourceManager()
	})
	return globalResourceManager
}
