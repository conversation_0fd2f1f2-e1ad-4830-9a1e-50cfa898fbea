package handlers

import (
	"context"
	"log/slog"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// StreamCoordinator 协调回调流和最终结果流，防止重复内容和竞态条件
type StreamCoordinator struct {
	ctx        context.Context
	cancel     context.CancelFunc
	ginContext *gin.Context
	clientGone <-chan bool

	// 流状态管理
	callbackActive bool
	finalActive    bool
	mu             sync.RWMutex

	// 消息去重和排序
	messageBuffer   []Message
	lastMessageTime time.Time
	bufferMu        sync.Mutex

	// 完成状态
	callbackDone chan struct{}
	finalDone    chan struct{}
	allDone      chan struct{}

	// 错误处理
	errorChan chan error
}

// NewStreamCoordinator 创建新的流协调器
func NewStreamCoordinator(ctx context.Context, c *gin.Context, clientGone <-chan bool) *StreamCoordinator {
	coordCtx, cancel := context.WithCancel(ctx)

	return &StreamCoordinator{
		ctx:           coordCtx,
		cancel:        cancel,
		ginContext:    c,
		clientGone:    clientGone,
		callbackDone:  make(chan struct{}),
		finalDone:     make(chan struct{}),
		allDone:       make(chan struct{}),
		errorChan:     make(chan error, 10),
		messageBuffer: make([]Message, 0, 100),
	}
}

// StartCallbackStream 启动回调流处理
func (sc *StreamCoordinator) StartCallbackStream(callbacks *RequestCallbacks) {
	sc.mu.Lock()
	sc.callbackActive = true
	sc.mu.Unlock()

	go func() {
		defer func() {
			sc.mu.Lock()
			sc.callbackActive = false
			sc.mu.Unlock()
			close(sc.callbackDone)
			slog.Info("回调流处理已完成")
		}()

		sc.handleCallbackMessages(callbacks)
	}()
}

// StartFinalStream 启动最终结果流处理
func (sc *StreamCoordinator) StartFinalStream(streamReader any) {
	sc.mu.Lock()
	sc.finalActive = true
	sc.mu.Unlock()

	go func() {
		defer func() {
			sc.mu.Lock()
			sc.finalActive = false
			sc.mu.Unlock()
			close(sc.finalDone)
			slog.Info("最终结果流处理已完成")
		}()

		sc.handleFinalMessages(streamReader)
	}()
}

// Wait 等待所有流处理完成
func (sc *StreamCoordinator) Wait() {
	go func() {
		// 等待两个流都完成
		<-sc.callbackDone
		<-sc.finalDone
		close(sc.allDone)
	}()

	// 监听完成或错误
	select {
	case <-sc.allDone:
		slog.Info("所有流处理已完成")
	case <-sc.ctx.Done():
		slog.Info("流协调器上下文已取消")
	case <-sc.clientGone:
		slog.Info("客户端已断开连接")
	case err := <-sc.errorChan:
		slog.Error("流处理出现错误", "error", err)
	case <-time.After(30 * time.Minute): // 设置超时
		slog.Warn("流处理超时")
	}
}

// Close 关闭协调器
func (sc *StreamCoordinator) Close() {
	sc.cancel()
}

// handleCallbackMessages 处理回调消息
func (sc *StreamCoordinator) handleCallbackMessages(callbacks *RequestCallbacks) {
	for {
		select {
		case <-sc.ctx.Done():
			return
		case <-sc.clientGone:
			slog.Info("客户端断开，停止回调消息处理")
			return
		case msg, ok := <-callbacks.responseHandler.GetChannel():
			if !ok {
				slog.Info("回调通道已关闭")
				return
			}

			// 转换并发送消息
			sseMsg := Message{
				Type:    string(msg.Type),
				Content: msg.Content,
			}

			if err := sc.sendMessage(sseMsg); err != nil {
				sc.errorChan <- err
				return
			}
		}
	}
}

// handleFinalMessages 处理最终结果消息
func (sc *StreamCoordinator) handleFinalMessages(streamReader any) {
	// 暂时的实现，等待真正的流处理逻辑
	slog.Info("最终结果流处理启动")

	// 发送完成消息
	msg := Message{
		Type:    "done",
		Content: "处理完成",
	}

	if err := sc.sendMessage(msg); err != nil {
		sc.errorChan <- err
	}
}

// sendMessage 发送消息并处理去重
func (sc *StreamCoordinator) sendMessage(msg Message) error {
	sc.bufferMu.Lock()
	defer sc.bufferMu.Unlock()

	// 简单的去重逻辑：检查是否与最近的消息重复
	if len(sc.messageBuffer) > 0 {
		lastMsg := sc.messageBuffer[len(sc.messageBuffer)-1]
		if lastMsg.Type == msg.Type && lastMsg.Content == msg.Content {
			// 重复消息，跳过
			return nil
		}
	}

	// 添加到缓冲区
	sc.messageBuffer = append(sc.messageBuffer, msg)
	sc.lastMessageTime = time.Now()

	// 发送消息
	if err := sendSSEMessage(sc.ginContext, msg); err != nil {
		return err
	}

	// 刷新响应
	sc.ginContext.Writer.Flush()

	return nil
}

// GetStats 获取协调器统计信息
func (sc *StreamCoordinator) GetStats() map[string]interface{} {
	sc.mu.RLock()
	defer sc.mu.RUnlock()

	sc.bufferMu.Lock()
	messageCount := len(sc.messageBuffer)
	sc.bufferMu.Unlock()

	return map[string]interface{}{
		"callback_active": sc.callbackActive,
		"final_active":    sc.finalActive,
		"message_count":   messageCount,
		"last_message":    sc.lastMessageTime,
	}
}
