package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"code.devops.xiaohongshu.com/cloud-native/talos/internal/service"
	taloscallbacks "code.devops.xiaohongshu.com/cloud-native/talos/pkg/components/callbacks"
	"github.com/gin-gonic/gin"
)

// Message represents a structured message for SSE
type Message struct {
	Type    string `json:"type"`
	Content string `json:"content"`
}

// RequestCallbacks 管理单个请求的回调处理器
type RequestCallbacks struct {
	logHandler      *taloscallbacks.LogHandler
	responseHandler *taloscallbacks.ResponseHandler
	ctx             context.Context
	cancel          context.CancelFunc
	wg              sync.WaitGroup
	mu              sync.Mutex
	closed          bool
}

// NewRequestCallbacks 创建新的请求回调处理器
func NewRequestCallbacks(ctx context.Context) *RequestCallbacks {
	reqCtx, cancel := context.WithCancel(ctx)
	return &RequestCallbacks{
		logHandler:      taloscallbacks.NewLogHandler(),
		responseHandler: taloscallbacks.NewResponseHandler(),
		ctx:             reqCtx,
		cancel:          cancel,
	}
}

// Close 清理回调处理器资源
func (rc *RequestCallbacks) Close() {
	rc.mu.Lock()
	defer rc.mu.Unlock()

	if rc.closed {
		return
	}
	rc.closed = true

	// 取消上下文
	rc.cancel()

	// 关闭通道
	rc.logHandler.CloseChannels()
	rc.responseHandler.CloseChannel()

	// 等待所有goroutine完成
	done := make(chan struct{})
	go func() {
		rc.wg.Wait()
		close(done)
	}()

	// 设置超时等待
	select {
	case <-done:
		slog.Info("回调处理器已成功关闭")
	case <-time.After(5 * time.Second):
		slog.Warn("回调处理器关闭超时")
	}
}

// StreamHandler handles streaming requests
type StreamHandler struct {
	ResearchService *service.ResearchService
}

// NewStreamHandler creates a new StreamHandler
func NewStreamHandler(researchService *service.ResearchService) *StreamHandler {
	return &StreamHandler{ResearchService: researchService}
}

// HandleStream 处理流式研究请求
// @Summary 处理流式研究请求
// @Description 处理流式研究请求并返回SSE流
// @Tags research
// @Accept json
// @Produce text/event-stream
// @Param request body service.Request true "Research request"
// @Success 200 {string} string "SSE流"
// @Router /api/v1/sse [post]
func (h *StreamHandler) HandleStream(c *gin.Context) {
	// 生成请求ID
	requestID := fmt.Sprintf("req_%d", time.Now().UnixNano())

	// 创建错误处理器
	errorHandler := NewErrorHandler()

	// 设置panic恢复
	defer errorHandler.HandlePanic(c, requestID)

	// 设置SSE响应头
	c.Writer.Header().Set("Content-Type", "text/event-stream")
	c.Writer.Header().Set("Cache-Control", "no-cache")
	c.Writer.Header().Set("Connection", "keep-alive")
	c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
	c.Writer.Header().Set("X-Request-ID", requestID)

	// 解析请求体
	var req service.Request
	if err := c.ShouldBindJSON(&req); err != nil {
		errorHandler.HandleError(c, fmt.Errorf("请求体格式错误: %w", err), requestID)
		return
	}

	if req.Content == "" {
		err := errors.New("请求内容不能为空")
		errorHandler.HandleError(c, err, requestID)
		return
	}

	// 创建请求级别的回调处理器
	callbacks := NewRequestCallbacks(c.Request.Context())
	defer callbacks.Close()

	// 监听客户端断开连接
	clientGone := c.Writer.CloseNotify()
	disconnectDetector := NewClientDisconnectionDetector(clientGone, func() {
		slog.Info("客户端断开连接，取消请求处理", "request_id", requestID)
		callbacks.Close()
	})
	defer disconnectDetector.Stop()

	// 创建流协调器
	coordinator := NewStreamCoordinator(callbacks.ctx, c, clientGone)
	defer coordinator.Close()

	// 启动日志回调流处理（用于调试）
	callbacks.logHandler.PrintStreamOutput()

	// 发送初始状态消息
	msg := Message{
		Type:    "status",
		Content: "开始处理请求...",
	}
	if err := sendSSEMessage(c, msg); err != nil {
		errorHandler.HandleError(c, fmt.Errorf("发送初始消息失败: %w", err), requestID)
		return
	}

	// 处理流式请求
	streamReader, err := h.ResearchService.StreamWithCallbacks(callbacks.ctx, req, callbacks.logHandler, callbacks.responseHandler)
	if err != nil {
		errorHandler.HandleError(c, fmt.Errorf("启动流处理失败: %w", err), requestID)
		return
	}
	defer streamReader.Close()

	// 启动协调的流处理
	coordinator.StartCallbackStream(callbacks)
	coordinator.StartFinalStream(streamReader)

	// 等待所有流处理完成，带超时控制
	done := make(chan struct{})
	go func() {
		coordinator.Wait()
		close(done)
	}()

	select {
	case <-done:
		slog.Info("流处理完成", "request_id", requestID)
	case <-time.After(30 * time.Minute): // 设置总体超时
		err := errors.New("流处理总体超时")
		errorHandler.HandleError(c, err, requestID)
	case <-clientGone:
		slog.Info("客户端断开连接，停止处理", "request_id", requestID)
	}
}

// handleResponseCallbackStream 处理响应回调流
func (h *StreamHandler) handleResponseCallbackStream(c *gin.Context, callbacks *RequestCallbacks, clientGone <-chan bool) {
	for {
		select {
		case <-clientGone:
			slog.Info("客户端已断开连接，停止响应回调流")
			return
		case <-callbacks.ctx.Done():
			slog.Info("上下文已取消，停止响应回调流")
			return
		case msg, ok := <-callbacks.responseHandler.GetChannel():
			if !ok {
				slog.Info("响应回调通道已关闭")
				return
			}

			// 将回调消息转换为SSE消息
			sseMsg := Message{
				Type:    string(msg.Type),
				Content: msg.Content,
			}

			if err := sendSSEMessage(c, sseMsg); err != nil {
				slog.Error("发送SSE消息失败", "error", err)
				return
			}

			// 确保响应被刷新到客户端
			c.Writer.Flush()
		}
	}
}

// handleFinalResultStream 处理最终结果流
func (h *StreamHandler) handleFinalResultStream(c *gin.Context, streamReader interface{}, clientGone <-chan bool) {
	slog.Info("最终结果流处理已启动")

	// 暂时发送完成消息，实际实现需要根据streamReader类型进行处理
	// TODO: 实现真正的流处理逻辑
	select {
	case <-clientGone:
		slog.Info("客户端已断开连接，停止最终结果流处理")
		return
	default:
		// 发送完成消息
		msg := Message{
			Type:    "done",
			Content: "处理完成",
		}
		if err := sendSSEMessage(c, msg); err != nil {
			slog.Error("发送完成消息失败", "error", err)
		}
	}
}

// sendSSEMessage 发送JSON编码的SSE事件消息
func sendSSEMessage(c *gin.Context, msg Message) error {
	data, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("序列化消息失败: %w", err)
	}

	// 以SSE格式写入事件
	_, err = fmt.Fprintf(c.Writer, "event: %s\ndata: %s\n\n", msg.Type, data)
	if err != nil {
		return fmt.Errorf("写入响应失败: %w", err)
	}

	return nil
}
