package handlers

import (
	"encoding/json"
	"fmt"
	"log"

	"code.devops.xiaohongshu.com/cloud-native/talos/internal/service"
	taloscallbacks "code.devops.xiaohongshu.com/cloud-native/talos/pkg/components/callbacks"
	"github.com/gin-gonic/gin"
)

// Message represents a structured message for SSE
type Message struct {
	Type    string `json:"type"`
	Content string `json:"content"`
}

// StreamHandler handles streaming requests
type StreamHandler struct {
	ResearchService *service.ResearchService
}

// NewStreamHandler creates a new StreamHandler
func NewStreamHandler(researchService *service.ResearchService) *StreamHandler {
	return &StreamHandler{ResearchService: researchService}
}

// HandleStream handles a streaming research request.
// @Summary 处理流式研究请求
// @Description 处理流式研究请求并返回SSE流
// @Tags research
// @Accept json
// @Produce text/event-stream
// @Param request body service.Request true "Research request"
// @Success 200 {string} string "SSE流"
// @Router /api/v1/sse [post]
func (h *<PERSON>Handler) HandleStream(c *gin.Context) {
	// Set headers for SSE
	c.Writer.Header().Set("Content-Type", "text/event-stream")
	c.Writer.Header().Set("Cache-Control", "no-cache")
	c.Writer.Header().Set("Connection", "keep-alive")
	c.Writer.Header().Set("Access-Control-Allow-Origin", "*")

	// Parse request body
	var req service.Request
	if err := c.ShouldBindJSON(&req); err != nil {
		msg := Message{
			Type:    "error",
			Content: fmt.Sprintf("Invalid request body: %v", err),
		}
		sendSSEMessage(c, msg)
		return
	}

	if req.Content == "" {
		msg := Message{
			Type:    "error",
			Content: "Content field is required in request body",
		}
		sendSSEMessage(c, msg)
		return
	}

	// Create a channel to handle client disconnection
	clientGone := c.Writer.CloseNotify()

	// Start processing stream
	taloscallbacks.DefaultLogHandler.PrintStreamOutput()

	// Process the streaming request
	streamReader, err := h.ResearchService.Stream(c.Request.Context(), req)
	if err != nil {
		msg := Message{
			Type:    "error",
			Content: fmt.Sprintf("Failed to start stream: %v", err),
		}
		sendSSEMessage(c, msg)
		return
	}
	defer streamReader.Close()

	// Send initial message
	msg := Message{
		Type:    "status",
		Content: "Starting handling...",
	}
	sendSSEMessage(c, msg)

	// Result stream
	go func() {
		for {
			select {
			case <-clientGone:
				log.Println("Client disconnected")
				return
			default:
				message, err := streamReader.Recv()
				if err != nil {
					if err.Error() == "EOF" {
						msg := Message{
							Type:    "done",
							Content: "Handling completed",
						}
						sendSSEMessage(c, msg)
					} else {
						msg := Message{
							Type:    "error",
							Content: fmt.Sprintf("Stream error: %v", err),
						}
						sendSSEMessage(c, msg)
						return
					}
				}

				// Send the answer content
				msg := Message{
					Type:    "answer",
					Content: message.Content,
				}
				sendSSEMessage(c, msg)

				// Ensure the response is flushed to the client
				c.Writer.Flush()
			}
		}
	}()

	taloscallbacks.DefaultLogHandler.WaitStreamOutput()
}

// sendSSEMessage sends a JSON-encoded message as an SSE event
func sendSSEMessage(c *gin.Context, msg Message) {
	data, err := json.Marshal(msg)
	if err != nil {
		log.Printf("Error marshaling message: %v", err)
		return
	}

	// Write the event in SSE format
	_, err = fmt.Fprintf(c.Writer, "event: %s\ndata: %s\n\n", msg.Type, data)
	if err != nil {
		log.Printf("Error writing to response: %v", err)
	}
}
