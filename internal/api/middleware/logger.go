package middleware

import (
	"context"
	"log/slog"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// ContextKey is a custom type for context keys.
type ContextKey string

// RequestIDKey is the key for the request ID in the context.
const RequestIDKey ContextKey = "request_id"

// StructuredLogger returns a gin middleware for structured logging
func StructuredLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Generate request ID
		requestID := uuid.New().String()
		// Store request ID in context
		c.Set(string(RequestIDKey), requestID)

		// Create context with request ID
		ctx := context.WithValue(c.Request.Context(), RequestIDKey, requestID)
		c.Request = c.Request.WithContext(ctx)

		// Start timer
		start := time.Now()
		path := c.Request.URL.Path

		// Log request
		slog.LogAttrs(ctx, slog.LevelInfo, "Request started",
			slog.String("request_id", requestID),
			slog.String("method", c.Request.Method),
			slog.String("path", path),
			slog.String("client_ip", c.ClientIP()),
			slog.String("user_agent", c.Request.UserAgent()),
		)

		// Process request
		c.Next()

		// Calculate latency
		latency := time.Since(start)

		// Get status
		status := c.Writer.Status()

		// Determine log level based on response status
		level := responseLogLevel(status)

		// Log response
		slog.LogAttrs(ctx, level, "Request completed",
			slog.String("request_id", requestID),
			slog.String("method", c.Request.Method),
			slog.String("path", path),
			slog.Int("status", status),
			slog.Duration("latency", latency),
			slog.Int("size", c.Writer.Size()),
			slog.Int("errors", len(c.Errors)),
		)
	}
}

// responseLogLevel determines the log level based on the HTTP status code.
func responseLogLevel(status int) slog.Level {
	switch {
	case status >= 400 && status < 500:
		// Client errors (4xx) are logged as warnings
		return slog.LevelWarn
	case status >= 500:
		// Server errors (5xx) are logged as errors
		return slog.LevelError
	default:
		// Successful responses (1xx, 2xx, 3xx) are logged as info
		return slog.LevelInfo
	}
}
