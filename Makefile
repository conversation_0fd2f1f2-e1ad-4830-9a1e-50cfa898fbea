# Makefile for Talos

# Environment variables
GO ?= go
BINARY_NAME ?= talos
MAIN_PATH ?= ./main.go
BUILD_DIR ?= ./bin
COVERAGE_FILE ?= coverage.out
GOLANGCI_LINT_VERSION ?= v1.55.2
SWAG_VERSION ?= latest
DOCKER_IMAGE ?= talos
DOCKER_TAG ?= latest
TEST_FLAGS ?= -v
TEST_PKGS ?= ./...

# Default target
.DEFAULT_GOAL := help

# Build targets
.PHONY: all
all: check test coverage ## Run all checks and tests

# Build
.PHONY: build
build: ## Build the application
	@mkdir -p $(BUILD_DIR)
	$(GO) build -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)

# Dependencies
.PHONY: deps
deps: ## Install project dependencies
	$(GO) mod download
	$(GO) mod verify

# Clean
.PHONY: clean
clean: ## Clean build artifacts
	@rm -rf $(BUILD_DIR)
	@rm -f $(COVERAGE_FILE) coverage.html

# Testing
.PHONY: test
test: ## Run tests
	$(GO) test $(TEST_FLAGS) $(TEST_PKGS)

.PHONY: test-race
test-race: TEST_FLAGS += -race
test-race: test ## Run tests with race detector

.PHONY: coverage
coverage: ## Generate test coverage report
	$(GO) test -coverprofile=$(COVERAGE_FILE) -covermode=count $(TEST_PKGS)
	$(GO) tool cover -html=$(COVERAGE_FILE) -o coverage.html
	@echo "Coverage report generated: coverage.html"

.PHONY: coverage-text
coverage-text: ## Show test coverage in terminal
	$(GO) test -coverprofile=$(COVERAGE_FILE) -covermode=count $(TEST_PKGS)
	$(GO) tool cover -func=$(COVERAGE_FILE)

# Code Quality
.PHONY: lint
lint: ## Run linter
	golangci-lint run --timeout=5m ./...

.PHONY: fmt
fmt: ## Format code
	$(GO) fmt ./...

.PHONY: vet
vet: ## Run go vet
	$(GO) vet ./...

.PHONY: check
check: fmt vet lint ## Run all code checks

# Tooling
.PHONY: install-tools
install-tools: ## Install development tools
	@echo "Installing tools..."
	@which golangci-lint || (curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(shell go env GOPATH)/bin $(GOLANGCI_LINT_VERSION))
	@which swag || $(GO) install github.com/swaggo/swag/cmd/swag@$(SWAG_VERSION)
	@echo "All tools installed successfully!"

# Swagger
.PHONY: swagger
swagger: ## Generate Swagger documentation
	@which swag || $(MAKE) install-tools
	swag init -g $(MAIN_PATH) -o ./docs
	@echo "Swagger documentation generated in ./docs"

.PHONY: swagger-validate
swagger-validate: swagger ## Validate Swagger documentation
	@docker run --rm -v $(PWD):/data -w /data stoplight/spectral lint docs/swagger.yaml

# Docker
.PHONY: docker-build
docker-build: ## Build Docker image
	docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) .

.PHONY: docker-run
docker-run: ## Run Docker container
	docker run -p 8080:8080 $(DOCKER_IMAGE):$(DOCKER_TAG)

# Kubernetes
.PHONY: colima-start
colima-start: ## Start Colima with Kubernetes
	colima start --cpu 4 --memory 8 --disk 50 --kubernetes
	@echo "Colima with Kubernetes started"

.PHONY: colima-stop
colima-stop: ## Stop Colima
	colima stop
	@echo "Colima stopped"

.PHONY: colima-delete
colima-delete: ## Delete Colima instance
	colima delete
	@echo "Colima instance deleted"

# Help
.PHONY: help
help: ## Show this help message
	@echo "Available targets:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}'
	@echo ""